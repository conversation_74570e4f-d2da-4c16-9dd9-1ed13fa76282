"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/_component/DataGridTableTrackSheet.tsx":
/*!****************************************************!*\
  !*** ./app/_component/DataGridTableTrackSheet.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaExclamationTriangle_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=FaExclamationTriangle,FaFilter,FaSearch!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _Pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Pagination */ \"(app-pages-browser)/./app/_component/Pagination.tsx\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-react */ \"(app-pages-browser)/./node_modules/ag-grid-react/dist/package/index.esm.mjs\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles/ag-grid.css */ \"(app-pages-browser)/./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles/ag-theme-alpine.css */ \"(app-pages-browser)/./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_customInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/customInput */ \"(app-pages-browser)/./components/ui/customInput.tsx\");\n/* harmony import */ var _barrel_optimize_names_BiHide_BiShow_react_icons_bi__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BiHide,BiShow!=!react-icons/bi */ \"(app-pages-browser)/./node_modules/react-icons/bi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LuSearchX_react_icons_lu__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=LuSearchX!=!react-icons/lu */ \"(app-pages-browser)/./node_modules/react-icons/lu/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _user_trackSheets_ticketing_system_CreateTicket__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../user/trackSheets/ticketing_system/CreateTicket */ \"(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _user_trackSheets_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../user/trackSheets/TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom cell renderer for selection checkbox\nconst SelectionCheckboxRenderer = (props)=>{\n    var _props_data;\n    const isSelected = props.node.isSelected();\n    const ticketId = (_props_data = props.data) === null || _props_data === void 0 ? void 0 : _props_data.ticketId;\n    const handleChange = (checked)=>{\n        props.node.setSelected(checked, false, true);\n    };\n    if (ticketId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center w-full h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/pms/manage_tickets/\".concat(ticketId, \"?from=tracksheets\"),\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                tabIndex: 0,\n                                className: \"focus:outline-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"flex items-center justify-center w-[22px] h-[22px] p-0.5 border-green-200 bg-green-50 text-green-700 hover:bg-green-100 transition cursor-pointer\",\n                                    style: {\n                                        minWidth: 22,\n                                        minHeight: 22\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                            side: \"right\",\n                            className: \"text-xs max-w-[180px]\",\n                            children: [\n                                \"A ticket already exists for this row.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Click to view the ticket in a new tab.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n            checked: isSelected,\n            onCheckedChange: handleChange,\n            \"aria-label\": \"Select row\",\n            className: \"h-4 w-4 border-2 border-primary rounded-sm bg-white\"\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SelectionCheckboxRenderer;\n// Custom header component for \"select all\" functionality\nconst HeaderSelectionCheckboxRenderer = (props)=>{\n    _s();\n    const [isChecked, setIsChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIndeterminate, setIsIndeterminate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const onSelectionChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const selectedNodes = props.api.getSelectedNodes();\n        let displayedNodeCount = 0;\n        props.api.forEachNodeAfterFilterAndSort(()=>{\n            displayedNodeCount++;\n        });\n        if (displayedNodeCount === 0) {\n            setIsChecked(false);\n            setIsIndeterminate(false);\n            return;\n        }\n        const allSelected = selectedNodes.length === displayedNodeCount;\n        const someSelected = selectedNodes.length > 0 && !allSelected;\n        setIsChecked(allSelected);\n        setIsIndeterminate(someSelected);\n    }, [\n        props.api\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        props.api.addEventListener(\"selectionChanged\", onSelectionChanged);\n        props.api.addEventListener(\"modelUpdated\", onSelectionChanged); // for filtering, sorting etc\n        return ()=>{\n            props.api.removeEventListener(\"selectionChanged\", onSelectionChanged);\n            props.api.removeEventListener(\"modelUpdated\", onSelectionChanged);\n        };\n    }, [\n        onSelectionChanged,\n        props.api\n    ]);\n    const handleChange = (checked)=>{\n        if (checked) {\n            props.api.forEachNodeAfterFilterAndSort((node)=>{\n                var _node_data;\n                if (!((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.ticketId)) {\n                    node.setSelected(true);\n                } else {\n                    node.setSelected(false);\n                }\n            });\n        } else {\n            props.api.forEachNodeAfterFilterAndSort((node)=>node.setSelected(false));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n            checked: isIndeterminate ? \"indeterminate\" : isChecked,\n            onCheckedChange: handleChange,\n            \"aria-label\": \"Select all rows\",\n            className: \"h-4 w-4 border-2 border-primary rounded-sm bg-white\"\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeaderSelectionCheckboxRenderer, \"jNhmhHqHBjDe+xUOk7p4amZYv0w=\");\n_c1 = HeaderSelectionCheckboxRenderer;\nconst DataGridTableTrackSheet = (param)=>{\n    let { columns, data, isLoading, showColDropDowns, filter, filter2, filter3 = false, filter3view, filter4, filter4view, filter5, filter5view, total, totalview, filter_column, filter_column2, filter_column3, filter_column4, totalPages, showPageEntries, className, filter1PlaceHolder, showSearchColumn = true, pageSize, customFieldsMap, selectedClients, showLegrandColumns, onColumnVisibilityChange } = param;\n    var _gridRef_current_api, _gridRef_current, _gridRef_current_api1, _gridRef_current1;\n    _s1();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(pageSize || 50);\n    const [selectedColumns, setSelectedColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isTicketModalOpen, setIsTicketModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const params = new URLSearchParams(searchParams);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { replace } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPage = Number(searchParams.get(\"page\")) || 1;\n    const currentPageSize = Number(searchParams.get(\"pageSize\")) || pageSize || 50;\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [columnData, setColumnData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columnVisibility, setColumnVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputValues, setInputValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [searchTerms, setSearchTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [filtersCollapsed, setFiltersCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const COLLAPSE_COUNT = 6;\n    const onSelectionChanged = ()=>{\n        if (gridRef.current && gridRef.current.api) {\n            setSelectedRows(gridRef.current.api.getSelectedRows());\n        }\n    };\n    const handleClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.deselectAll();\n        }\n        setSelectedRows([]);\n        setIsTicketModalOpen(false);\n    }, []);\n    const processedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return data === null || data === void 0 ? void 0 : data.map((item, index)=>({\n                ...item,\n                stableId: (currentPage - 1) * currentPageSize + index + 1\n            }));\n    }, [\n        data,\n        currentPage,\n        currentPageSize\n    ]);\n    const columnsWithSerialNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return [\n            {\n                width: 80,\n                pinned: \"left\",\n                lockVisible: true,\n                suppressHeaderMenuButton: true,\n                headerName: \"\",\n                headerComponent: HeaderSelectionCheckboxRenderer,\n                cellRenderer: SelectionCheckboxRenderer,\n                suppressMovable: true,\n                suppressSizeToFit: true,\n                resizable: false,\n                suppressNavigable: true,\n                suppressMenu: true,\n                suppressColumnsToolPanel: true,\n                suppressAutoSize: true,\n                suppressCellSelection: true\n            },\n            {\n                headerName: \"Sr. No.\",\n                field: \"stableId\",\n                sortable: true,\n                width: 80,\n                minWidth: 80,\n                maxWidth: 80,\n                cellStyle: {\n                    textAlign: \"center\"\n                },\n                pinned: \"left\",\n                comparator: (valueA, valueB)=>valueA - valueB\n            },\n            ...columns.filter((col)=>col.field !== \"sr_no\")\n        ];\n    }, [\n        columns\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize visibility state for all columns only once\n        if (!isInitialized) {\n            const initialVisibility = {};\n            columnsWithSerialNumber.forEach((col)=>{\n                initialVisibility[col.field] = true;\n            });\n            setColumnVisibility(initialVisibility);\n            onColumnVisibilityChange === null || onColumnVisibilityChange === void 0 ? void 0 : onColumnVisibilityChange(initialVisibility);\n            setIsInitialized(true);\n        }\n    }, [\n        columnsWithSerialNumber,\n        isInitialized,\n        onColumnVisibilityChange\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            if (isLoading) {\n                gridRef.current.api.showLoadingOverlay();\n            } else if (!processedData || processedData.length === 0) {\n                gridRef.current.api.showNoRowsOverlay();\n            } else {\n                gridRef.current.api.hideOverlay();\n            }\n        }\n    }, [\n        isLoading,\n        processedData\n    ]);\n    const handlePageChange = (e)=>{\n        const newPageSize = parseInt(e.target.value);\n        setPage(newPageSize);\n        if (totalPages) {\n            params.set(\"pageSize\", newPageSize === null || newPageSize === void 0 ? void 0 : newPageSize.toString());\n            replace(\"\".concat(pathname, \"?\").concat(params.toString()));\n        }\n    };\n    const handleColumnSelection = (columnKey, columnHeader)=>{\n        setSelectedColumns((prevSelectedColumns)=>{\n            let updatedColumns;\n            if (prevSelectedColumns.includes(columnKey)) {\n                updatedColumns = prevSelectedColumns.filter((col)=>col !== columnKey);\n                const updatedParams = new URLSearchParams(searchParams);\n                if (columnHeader === \"Received Date\") {\n                    updatedParams.delete(\"recievedFDate\");\n                    updatedParams.delete(\"recievedTDate\");\n                } else {\n                    updatedParams.delete(columnHeader);\n                }\n                replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            } else {\n                updatedColumns = [\n                    ...prevSelectedColumns,\n                    columnKey\n                ];\n                const updatedParams = new URLSearchParams(searchParams);\n                if (columnHeader === \"Received Date\") {\n                    updatedParams.set(\"recievedFDate\", \"\");\n                    updatedParams.set(\"recievedTDate\", \"\");\n                } else {\n                    updatedParams.set(columnHeader, \"\");\n                }\n                replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            }\n            return updatedColumns;\n        });\n    };\n    const handleKeyDown = (e, columnName)=>{\n        var _inputValues_columnName, _searchTerms_columnName;\n        let paramKey = columnName;\n        if (!paramKey.startsWith(\"customField_\")) {\n            const col = columnsWithSerialNumber.find((c)=>c.headerName === columnName || c.field === columnName);\n            if (col && col.field && col.field.startsWith(\"customField_\")) {\n                paramKey = col.field;\n            }\n        }\n        const currentInput = (_inputValues_columnName = inputValues[columnName]) === null || _inputValues_columnName === void 0 ? void 0 : _inputValues_columnName.trim();\n        if (e.key === \"Enter\") {\n            var _searchTerms_columnName1;\n            e.preventDefault();\n            if (currentInput && !((_searchTerms_columnName1 = searchTerms[columnName]) === null || _searchTerms_columnName1 === void 0 ? void 0 : _searchTerms_columnName1.includes(currentInput))) {\n                const updated = [\n                    ...searchTerms[columnName] || [],\n                    currentInput\n                ];\n                updateSearchParams(columnName, updated);\n                setSearchTerms({\n                    ...searchTerms,\n                    [columnName]: updated\n                });\n                setInputValues({\n                    ...inputValues,\n                    [columnName]: \"\"\n                });\n            }\n        }\n        if (e.key === \"Backspace\" && !currentInput && ((_searchTerms_columnName = searchTerms[columnName]) === null || _searchTerms_columnName === void 0 ? void 0 : _searchTerms_columnName.length) > 0) {\n            const updated = [\n                ...searchTerms[columnName]\n            ];\n            updated.pop();\n            updateSearchParams(columnName, updated);\n            setSearchTerms({\n                ...searchTerms,\n                [columnName]: updated\n            });\n        }\n    };\n    const handleRemoveTerm = (columnName, term)=>{\n        const col = columnsWithSerialNumber.find((c)=>c.headerName === columnName || c.field === columnName);\n        const key = col ? col.headerName : columnName;\n        let paramKey = columnName;\n        if (!paramKey.startsWith(\"customField_\")) {\n            if (col && col.field && col.field.startsWith(\"customField_\")) {\n                paramKey = col.field;\n            }\n        }\n        if (paramKey.startsWith(\"customField_\")) {\n            const newSearchTerms = {\n                ...searchTerms\n            };\n            Object.keys(newSearchTerms).forEach((k)=>{\n                if (k === key || k === paramKey || k.startsWith(paramKey)) {\n                    newSearchTerms[k] = [];\n                }\n            });\n            setSearchTerms(newSearchTerms);\n            setInputValues((prev)=>{\n                const newValues = {\n                    ...prev\n                };\n                Object.keys(newValues).forEach((k)=>{\n                    if (k === key || k === paramKey || k.startsWith(paramKey)) {\n                        newValues[k] = \"\";\n                    }\n                });\n                return newValues;\n            });\n            const updatedParams = new URLSearchParams(searchParams);\n            updatedParams.delete(paramKey);\n            replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            return;\n        }\n        const updated = (searchTerms[paramKey] || []).filter((t)=>t !== term);\n        updateSearchParams(paramKey, updated);\n        setSearchTerms({\n            ...searchTerms,\n            [paramKey]: updated\n        });\n    };\n    const updateSearchParams = (columnName, terms)=>{\n        var _gridRef_current;\n        let paramKey = columnName;\n        if (!paramKey.startsWith(\"customField_\")) {\n            const col = columnsWithSerialNumber.find((c)=>c.headerName === columnName || c.field === columnName);\n            if (col && col.field && col.field.startsWith(\"customField_\")) {\n                paramKey = col.field;\n            }\n        }\n        const updatedParams = new URLSearchParams(searchParams);\n        if (terms.length > 0) {\n            updatedParams.set(paramKey, terms.join(\",\"));\n            updatedParams.set(\"page\", \"1\");\n        } else {\n            updatedParams.delete(paramKey);\n        }\n        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n        if ((_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : _gridRef_current.api) {\n            gridRef.current.api.setColumnsVisible([\n                columnName\n            ], terms.length > 0);\n        }\n    };\n    const filterColumns = selectedColumns.length ? selectedColumns.map((columnKey)=>columnsWithSerialNumber.find((col)=>col.field === columnKey)).filter((col)=>col !== undefined) : [];\n    const toggleColumnVisibility = (field, isVisible)=>{\n        var _gridRef_current;\n        setColumnVisibility((prevVisibility)=>{\n            const newVisibility = {\n                ...prevVisibility,\n                [field]: isVisible\n            };\n            onColumnVisibilityChange === null || onColumnVisibilityChange === void 0 ? void 0 : onColumnVisibilityChange(newVisibility);\n            return newVisibility;\n        });\n        // Update AG Grid directly without using effect\n        if ((_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : _gridRef_current.api) {\n            gridRef.current.api.setColumnsVisible([\n                field\n            ], isVisible);\n        }\n    };\n    const { warningFilter, setWarningFilter } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_user_trackSheets_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__.TrackSheetContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"animate-in fade-in duration-1000\",\n        children: [\n            selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 left-1/2 -translate-x-1/2 z-20 bg-background border border-primary shadow-lg rounded-lg px-4 py-2 flex items-center gap-4 animate-in fade-in slide-in-from-bottom-2 duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-primary\",\n                        children: [\n                            selectedRows.length,\n                            \" item(s) selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        variant: \"default\",\n                        size: \"sm\",\n                        onClick: ()=>setIsTicketModalOpen(true),\n                        children: \"Create Ticket\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"h-7 w-7\",\n                        onClick: handleClearSelection,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuSearchX_react_icons_lu__WEBPACK_IMPORTED_MODULE_18__.LuSearchX, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Clear Selection\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 504,\n                columnNumber: 9\n            }, undefined),\n            isTicketModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_trackSheets_ticketing_system_CreateTicket__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: isTicketModalOpen,\n                onClose: ()=>setIsTicketModalOpen(false),\n                onClearSelection: handleClearSelection,\n                selectedRows: selectedRows\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 528,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between w-full lg:w-full lg:flex lg:justify-end mb-3 animate-in fade-in duration-1000 gap-5\",\n                children: [\n                    selectedClients.length > 0 && showLegrandColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    className: \"h-10 px-4 text-sm font-medium flex items-center gap-2 text-gray-800 border border-gray-300 bg-white hover:bg-gray-50 hover:border-gray-400 shadow-sm rounded-md transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaExclamationTriangle_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__.FaExclamationTriangle, {\n                                            className: \"text-red-500 text-base\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        warningFilter === \"true\" ? \"Non-Empty Warnings\" : warningFilter === \"false\" ? \"Empty Warnings\" : \"Warnings\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"w-60 bg-white border border-gray-200 rounded-md shadow-lg dark:bg-gray-900 dark:border-gray-800\",\n                                align: \"end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: ()=>setWarningFilter(\"true\"),\n                                        className: \"text-sm px-3 py-2 cursor-pointer transition-colors \".concat(warningFilter === \"true\" ? \"bg-red-100 font-semibold\" : \"hover:bg-gray-100\"),\n                                        children: \"Show Non-Empty Warnings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: ()=>setWarningFilter(\"false\"),\n                                        className: \"text-sm px-3 py-2 cursor-pointer transition-colors \".concat(warningFilter === \"false\" ? \"bg-red-100 font-semibold\" : \"hover:bg-gray-100\"),\n                                        children: \"Show Empty Warnings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: ()=>setWarningFilter(null),\n                                        className: \"text-sm px-3 py-2 cursor-pointer transition-colors \".concat(warningFilter === null ? \"bg-red-100 font-semibold\" : \"hover:bg-gray-100\"),\n                                        children: \"Show All\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 11\n                    }, undefined),\n                    filtersCollapsed && showPageEntries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: page,\n                        onChange: handlePageChange,\n                        className: \" border-2 rounded-md  items-center justify-center cursor-pointer h-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 10,\n                                children: \"10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 15,\n                                children: \"15\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 25,\n                                children: \"25\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 50,\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 100,\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 250,\n                                children: \"250\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 500,\n                                children: \"500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 1000,\n                                children: \"1000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 1500,\n                                children: \"1500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 2000,\n                                children: \"2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 11\n                    }, undefined),\n                    filtersCollapsed && showColDropDowns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"border-2 rounded-md flex items-center justify-center  cursor-pointer h-10 w-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaExclamationTriangle_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__.FaFilter, {}, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 overflow-y-auto max-h-80\",\n                                onSelect: (e)=>e.preventDefault(),\n                                children: columnsWithSerialNumber.filter((column)=>column.hideable !== false).map((column)=>/*#__PURE__*/ {\n                                    var _columnVisibility_column_field;\n                                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuCheckboxItem, {\n                                        className: \"capitalize cursor-pointer\",\n                                        checked: (_columnVisibility_column_field = columnVisibility[column.field]) !== null && _columnVisibility_column_field !== void 0 ? _columnVisibility_column_field : true,\n                                        onCheckedChange: (value)=>{\n                                            toggleColumnVisibility(column.field, value);\n                                        },\n                                        onSelect: (e)=>e.preventDefault(),\n                                        children: column.headerName || column.field\n                                    }, column.field, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 612,\n                        columnNumber: 11\n                    }, undefined),\n                    filtersCollapsed && showSearchColumn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"border-2 rounded-md flex items-center justify-center  cursor-pointer h-10 w-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaExclamationTriangle_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__.FaSearch, {\n                                        className: \"text-base\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 643,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"mt-2 bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 max-h-96 min-w-[260px]\",\n                                onSelect: (e)=>e.preventDefault(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 sticky top-0 z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-semibold text-sm\",\n                                                        children: \"Select Columns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            selectedColumns.length,\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Search columns...\",\n                                                value: columnData,\n                                                onChange: (e)=>setColumnData(e.target.value),\n                                                className: \"mt-1 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 mb-2 \",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-xs text-blue-600 underline\",\n                                                    type: \"button\",\n                                                    onClick: ()=>setSelectedColumns(columnsWithSerialNumber.filter((col)=>col.field !== \"action\" && col.field !== \"sr_no\" && col.field !== \"stableId\").map((col)=>col.field)),\n                                                    children: \"Select All\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-y-auto max-h-60\",\n                                        children: columnsWithSerialNumber.filter((item)=>{\n                                            var _this;\n                                            return item.field !== \"action\" && item.field !== \"sr_no\" && item.field !== \"stableId\" && (!columnData || ((_this = item.headerName || item.field) === null || _this === void 0 ? void 0 : _this.toLowerCase().includes(columnData.toLowerCase())));\n                                        }).map((item, id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuCheckboxItem, {\n                                                checked: selectedColumns.includes(item.field),\n                                                onCheckedChange: ()=>handleColumnSelection(item.field, item.headerName || item.field),\n                                                className: \"capitalize cursor-pointer\",\n                                                onSelect: (e)=>e.preventDefault(),\n                                                children: item.headerName\n                                            }, id, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, undefined),\n                    filterColumns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-nowrap gap-2 items-center overflow-x-auto bg-gray-50 border border-gray-200 rounded-md py-2 px-2\",\n                            style: {\n                                scrollbarWidth: \"thin\",\n                                minHeight: 50\n                            },\n                            children: [\n                                filterColumns.length > COLLAPSE_COUNT && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 sticky left-0 z-10 bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs border border-gray-300 mr-2\",\n                                        type: \"button\",\n                                        onClick: ()=>setFiltersCollapsed((prev)=>!prev),\n                                        children: filtersCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BiHide_BiShow_react_icons_bi__WEBPACK_IMPORTED_MODULE_20__.BiShow, {\n                                            className: \"w-5 h-5\",\n                                            title: \"Show Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 23\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BiHide_BiShow_react_icons_bi__WEBPACK_IMPORTED_MODULE_20__.BiHide, {\n                                            className: \"w-5 h-5\",\n                                            title: \"Hide Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 17\n                                }, undefined),\n                                filterColumns.slice(0, filtersCollapsed && filterColumns.length > COLLAPSE_COUNT ? COLLAPSE_COUNT : filterColumns.length).map((col, index)=>{\n                                    var _customFieldsMap_customFieldId_type, _customFieldsMap_customFieldId, _searchTerms_col_headerName, _searchTerms_col_headerName1;\n                                    if (col.headerName === \"Received Date\") {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Recieved Date (From)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"recievedFDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"recievedFDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Recieved Date (To)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"recievedTDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"recievedTDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(\"recievedFDate\") || searchParams.get(\"recievedTDate\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2\",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(\"recievedFDate\");\n                                                        updatedParams.delete(\"recievedTDate\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    if (col.headerName === \"Invoice Date\") {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Invoice Date (From)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"invoiceFDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"invoiceFDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Invoice Date (To)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"invoiceTDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"invoiceTDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(\"invoiceFDate\") || searchParams.get(\"invoiceTDate\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2\",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(\"invoiceFDate\");\n                                                        updatedParams.delete(\"invoiceTDate\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    if (col.headerName === \"Shipment Date\") {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Shipment Date (From)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"shipmentFDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"shipmentFDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Shipment Date (To)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"shipmentTDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"shipmentTDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(\"shipmentFDate\") || searchParams.get(\"shipmentTDate\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2 \",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(\"shipmentFDate\");\n                                                        updatedParams.delete(\"shipmentTDate\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    const isCustomField = col.field.startsWith(\"customField_\");\n                                    const customFieldId = isCustomField ? col.field.replace(\"customField_\", \"\") : null;\n                                    const isDateType = isCustomField && customFieldsMap && ((_customFieldsMap_customFieldId = customFieldsMap[customFieldId]) === null || _customFieldsMap_customFieldId === void 0 ? void 0 : (_customFieldsMap_customFieldId_type = _customFieldsMap_customFieldId.type) === null || _customFieldsMap_customFieldId_type === void 0 ? void 0 : _customFieldsMap_customFieldId_type.toLowerCase()) === \"date\";\n                                    if (isDateType) {\n                                        const fromKey = \"customField_\".concat(customFieldId, \"_from\");\n                                        const toKey = \"customField_\".concat(customFieldId, \"_to\");\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: [\n                                                        customFieldsMap[customFieldId].name,\n                                                        \" (From)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 948,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(fromKey) || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        if (e.target.value) updatedParams.set(fromKey, e.target.value);\n                                                        else updatedParams.delete(fromKey);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: [\n                                                        customFieldsMap[customFieldId].name,\n                                                        \" (To)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 966,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(toKey) || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        if (e.target.value) updatedParams.set(toKey, e.target.value);\n                                                        else updatedParams.delete(toKey);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(fromKey) || searchParams.get(toKey)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"mt-2 absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg\",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(fromKey);\n                                                        updatedParams.delete(toKey);\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex- w-full relative  \".concat(filtersCollapsed ? \"min-w-[100px] md:w-[calc(20%-1rem)]\" : \"min-w-[220px] md:w-[calc(25%-1rem)]\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap items-center gap-1 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md  px-1 focus-within:ring-0 focus-within:ring-blue-600 focus-within:border-blue-600 shadow-sm\",\n                                                children: [\n                                                    ((_searchTerms_col_headerName = searchTerms[col === null || col === void 0 ? void 0 : col.headerName]) === null || _searchTerms_col_headerName === void 0 ? void 0 : _searchTerms_col_headerName.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: searchTerms[col.headerName].map((term, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \" flex items-center bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 rounded-full text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-1 truncate max-w-xs\",\n                                                                        children: term\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                                        lineNumber: 1025,\n                                                                        columnNumber: 35\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: (e)=>{\n                                                                            e.preventDefault();\n                                                                            handleRemoveTerm(col.headerName, term);\n                                                                        },\n                                                                        className: \"  text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100\",\n                                                                        title: \"Remove\",\n                                                                        children: \"\\xd7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                                        lineNumber: 1028,\n                                                                        columnNumber: 35\n                                                                    }, undefined)\n                                                                ]\n                                                            }, i, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                                lineNumber: 1021,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_customInput__WEBPACK_IMPORTED_MODULE_10__.CustomInput, {\n                                                        value: inputValues[col === null || col === void 0 ? void 0 : col.headerName] || \"\",\n                                                        onChange: (e)=>setInputValues({\n                                                                ...inputValues,\n                                                                [col.headerName]: e.target.value\n                                                            }),\n                                                        onKeyDown: (e)=>handleKeyDown(e, col.headerName),\n                                                        placeholder: ((_searchTerms_col_headerName1 = searchTerms[col === null || col === void 0 ? void 0 : col.headerName]) === null || _searchTerms_col_headerName1 === void 0 ? void 0 : _searchTerms_col_headerName1.length) > 0 ? \"\" : \"Search \".concat(col.headerName, \"...\"),\n                                                        className: \"flex-1 min-w-[30px] bg-transparent border-none focus:outline-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    inputValues[col.headerName] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"ml-1 text-gray-400 hover:text-gray-700 text-lg\",\n                                                        type: \"button\",\n                                                        onClick: ()=>{\n                                                            setInputValues({\n                                                                ...inputValues,\n                                                                [col.headerName]: \"\"\n                                                            });\n                                                            handleRemoveTerm(col.headerName, inputValues[col.headerName]);\n                                                        },\n                                                        title: \"Clear\",\n                                                        children: \"\\xd7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 1059,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 1017,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 21\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 ml-2 sticky right-0 z-10 bg-gray-50 flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs border border-gray-300\",\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setInputValues({});\n                                            setSearchTerms({});\n                                            setSelectedColumns([]);\n                                            setColumnData(\"\");\n                                            setFiltersCollapsed(true);\n                                            setColumnVisibility({});\n                                            const updatedParams = new URLSearchParams(searchParams);\n                                            filterColumns.forEach((col)=>{\n                                                updatedParams.delete(col.headerName);\n                                                if (col.field.startsWith(\"customField_\")) {\n                                                    updatedParams.delete(col.field);\n                                                    updatedParams.delete(\"\".concat(col.field, \"_from\"));\n                                                    updatedParams.delete(\"\".concat(col.field, \"_to\"));\n                                                }\n                                                if (col.headerName === \"Received Date\") {\n                                                    updatedParams.delete(\"recievedFDate\");\n                                                    updatedParams.delete(\"recievedTDate\");\n                                                }\n                                                if (col.headerName === \"Invoice Date\") {\n                                                    updatedParams.delete(\"invoiceFDate\");\n                                                    updatedParams.delete(\"invoiceTDate\");\n                                                }\n                                                if (col.headerName === \"Shipment Date\") {\n                                                    updatedParams.delete(\"shipmentFDate\");\n                                                    updatedParams.delete(\"shipmentTDate\");\n                                                }\n                                            });\n                                            replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                        },\n                                        title: \"Reset All Filters\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuSearchX_react_icons_lu__WEBPACK_IMPORTED_MODULE_18__.LuSearchX, {\n                                            className: \"text-red-500 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 1082,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 11\n                    }, undefined),\n                    filter && filterColumns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: filterColumns.map((column, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: filter1PlaceHolder ? filter1PlaceHolder : filter_column,\n                                value: inputValues[column.headerName] || \"\",\n                                onChange: (e)=>setInputValues({\n                                        ...inputValues,\n                                        [column.headerName]: e.target.value\n                                    }),\n                                onKeyDown: (e)=>handleKeyDown(e, column.headerName),\n                                className: \"w-[20%] dark:bg-gray-700 !outline-main-color\"\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 1127,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false),\n                    filter2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: filterColumns.map((column, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"\".concat(filter_column2),\n                                value: inputValues[column.headerName] || \"\",\n                                onChange: (e)=>setInputValues({\n                                        ...inputValues,\n                                        [column.headerName]: e.target.value\n                                    }),\n                                onKeyDown: (e)=>handleKeyDown(e, column.headerName),\n                                className: \"w-[20%] dark:bg-gray-700 !outline-main-color\"\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 1148,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, undefined),\n            total && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: totalview\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 1166,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ag-theme-alpine custom-ag-grid\",\n                    style: {\n                        height: \"100vh\",\n                        width: \"100%\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_6__.AgGridReact, {\n                        ref: gridRef,\n                        rowData: processedData,\n                        columnDefs: columnsWithSerialNumber,\n                        headerHeight: 35,\n                        rowSelection: \"multiple\",\n                        suppressRowClickSelection: true,\n                        onSelectionChanged: onSelectionChanged,\n                        enableCellTextSelection: true,\n                        defaultColDef: {\n                            sortable: true,\n                            resizable: true,\n                            cellStyle: {\n                                borderRight: \"1px solid #ddd\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 1173,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                    lineNumber: 1169,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 1168,\n                columnNumber: 7\n            }, undefined),\n            data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Pagination__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                currentPage: totalPages ? Number(params.get(\"page\")) || 1 : (((_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : (_gridRef_current_api = _gridRef_current.api) === null || _gridRef_current_api === void 0 ? void 0 : _gridRef_current_api.paginationGetCurrentPage()) || 0) + 1,\n                totalPages: totalPages ? totalPages : ((_gridRef_current1 = gridRef.current) === null || _gridRef_current1 === void 0 ? void 0 : (_gridRef_current_api1 = _gridRef_current1.api) === null || _gridRef_current_api1 === void 0 ? void 0 : _gridRef_current_api1.paginationGetTotalPages()) || 1,\n                onPageChange: (page)=>{\n                    if (gridRef.current) {\n                        var _gridRef_current_api, _gridRef_current;\n                        gridRef === null || gridRef === void 0 ? void 0 : (_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : (_gridRef_current_api = _gridRef_current.api) === null || _gridRef_current_api === void 0 ? void 0 : _gridRef_current_api.paginationGoToPage(page - 1);\n                    }\n                    if (totalPages) {\n                        params.set(\"page\", page.toString());\n                        replace(\"\".concat(pathname, \"?\").concat(params.toString()));\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 1192,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n        lineNumber: 502,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DataGridTableTrackSheet, \"CP4z04e5pLoiYKiNqiAO2xHXEow=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c2 = DataGridTableTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DataGridTableTrackSheet);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"SelectionCheckboxRenderer\");\n$RefreshReg$(_c1, \"HeaderSelectionCheckboxRenderer\");\n$RefreshReg$(_c2, \"DataGridTableTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/_component/DataGridTableTrackSheet.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/user/trackSheets/column.tsx":
/*!*****************************************!*\
  !*** ./app/user/trackSheets/column.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var ag_grid_community__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ag-grid-community */ \"(app-pages-browser)/./node_modules/ag-grid-community/dist/package/main.esm.mjs\");\n/* harmony import */ var luxon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! luxon */ \"(app-pages-browser)/./node_modules/luxon/src/luxon.js\");\n/* harmony import */ var _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/_component/PinnedHeader */ \"(app-pages-browser)/./app/_component/PinnedHeader.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _UpdateTrackSheet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UpdateTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/UpdateTrackSheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertOctagon,AlertTriangle,Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertOctagon,AlertTriangle,Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertOctagon,AlertTriangle,Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/octagon-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertOctagon,AlertTriangle,Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertOctagon,AlertTriangle,Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _app_component_DeleteRow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/_component/DeleteRow */ \"(app-pages-browser)/./app/_component/DeleteRow.tsx\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_permissionWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/permissionWrapper */ \"(app-pages-browser)/./lib/permissionWrapper.ts\");\n/* harmony import */ var _CreateManifestDetail__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CreateManifestDetail */ \"(app-pages-browser)/./app/user/trackSheets/CreateManifestDetail.tsx\");\n/* harmony import */ var _barrel_optimize_names_MdCreateNewFolder_react_icons_md__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=MdCreateNewFolder!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nag_grid_community__WEBPACK_IMPORTED_MODULE_12__.ModuleRegistry.registerModules([\n    ag_grid_community__WEBPACK_IMPORTED_MODULE_12__.AllCommunityModule\n]);\n// Copy Button Component for File Path\nconst CopyButton = (param)=>{\n    let { text, disabled = false } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const handleCopy = async (e)=>{\n        e.stopPropagation(); // Prevent row selection\n        if (disabled || !text || text === \"No file path generated\") {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"No file path to copy\");\n            return;\n        }\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopied(true);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"File path copied to clipboard!\");\n            // Reset the copied state after 2 seconds\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to copy file path\");\n            /* eslint-disable */ console.error(...oo_tx(\"2012648454_64_6_64_49_11\", \"Failed to copy text: \", err));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleCopy,\n        disabled: disabled,\n        className: \"\\n        ml-2 p-1 rounded transition-all duration-200 flex-shrink-0\\n        \".concat(disabled ? \"text-gray-400 cursor-not-allowed\" : \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 active:bg-blue-100\", \"\\n      \"),\n        title: disabled ? \"No file path to copy\" : \"Copy file path\",\n        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"w-4 h-4 text-green-600\"\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n            lineNumber: 83,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n            lineNumber: 85,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CopyButton, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = CopyButton;\n// --- New: WarningIconWithTooltip component ---\nconst WarningIconWithPopover = (param)=>{\n    let { warnings } = param;\n    if (!warnings || warnings.length === 0) return null;\n    // Group warnings by severity\n    const grouped = warnings.reduce((acc, w)=>{\n        acc[w.severity] = acc[w.severity] || [];\n        acc[w.severity].push(w);\n        return acc;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"cursor-pointer flex justify-center items-center w-full h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"w-5 h-5 text-red-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                className: \"max-w-xs p-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs font-semibold mb-1 text-red-700\",\n                        children: \"Warnings:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    [\n                        \"CRITICAL\",\n                        \"HIGH\",\n                        \"MEDIUM\"\n                    ].map((sev)=>grouped[sev] && grouped[sev].length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: sev === \"CRITICAL\" ? \"flex items-center gap-1 text-red-900 font-bold\" : sev === \"HIGH\" ? \"flex items-center gap-1 text-red-600 font-bold\" : \"flex items-center gap-1 text-yellow-600 font-bold\",\n                                    children: [\n                                        sev === \"CRITICAL\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 42\n                                        }, undefined),\n                                        sev === \"HIGH\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 38\n                                        }, undefined),\n                                        sev === \"MEDIUM\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertOctagon_AlertTriangle_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 40\n                                        }, undefined),\n                                        sev\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"ml-5 list-disc text-xs\",\n                                    children: grouped[sev].map((w, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: w.message\n                                        }, i, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, sev, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 15\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = WarningIconWithPopover;\nWarningIconWithPopover.displayName = \"WarningIconWithPopover\";\n// --- New: ManifestActionButton to correctly handle dialog state ---\nconst ManifestActionButton = (param)=>{\n    let { TrackSheet, userData } = param;\n    _s1();\n    const [isDialogOpen, setIsDialogOpen] = react__WEBPACK_IMPORTED_MODULE_5___default().useState(false);\n    const manifestDetailRef = react__WEBPACK_IMPORTED_MODULE_5___default().useRef(null);\n    const handleOpenDialog = ()=>{\n        setIsDialogOpen(true);\n        setTimeout(()=>{\n            var _manifestDetailRef_current;\n            (_manifestDetailRef_current = manifestDetailRef.current) === null || _manifestDetailRef_current === void 0 ? void 0 : _manifestDetailRef_current.fetchManifestDetails(TrackSheet === null || TrackSheet === void 0 ? void 0 : TrackSheet.id);\n        }, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                variant: \"customButton\",\n                className: \"cursor-pointer capitalize h-4 w-4 text-gray-600\",\n                onClick: handleOpenDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdCreateNewFolder_react_icons_md__WEBPACK_IMPORTED_MODULE_18__.MdCreateNewFolder, {}, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            isDialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateManifestDetail__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                ref: manifestDetailRef,\n                trackSheetId: TrackSheet === null || TrackSheet === void 0 ? void 0 : TrackSheet.id,\n                isDialogOpen: isDialogOpen,\n                setIsDialogOpen: setIsDialogOpen,\n                userData: userData\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s1(ManifestActionButton, \"N3MudmYQl6bbnbNO7FmDMoPnyL4=\");\n_c2 = ManifestActionButton;\nManifestActionButton.displayName = \"ManifestActionButton\";\nconst Column = (permissions, setDeletedData, deleteData, carrierDataUpdate, clientDataUpdate, userData, param)=>{\n    let { customFieldsMap, showOrcaColumns, showLegrandColumns } = param;\n    const baseColumns = [\n        {\n            field: \"client\",\n            headerName: \"Client\",\n            valueGetter: (params)=>{\n                var _params_data_client, _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_client = _params_data.client) === null || _params_data_client === void 0 ? void 0 : _params_data_client.client_name) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"company\",\n            headerName: \"Company\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.company) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"division\",\n            headerName: \"Division\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.division) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"carrier\",\n            headerName: \"Carrier\",\n            valueGetter: (params)=>{\n                var _params_data_carrier, _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_carrier = _params_data.carrier) === null || _params_data_carrier === void 0 ? void 0 : _params_data_carrier.name) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"ftpFileName\",\n            headerName: \"FTP File Name\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.ftpFileName) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"ftpPage\",\n            headerName: \"FTP Page\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.ftpPage) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"filePath\",\n            headerName: \"File Path\",\n            cellRenderer: (params)=>{\n                var _params_data;\n                const filePath = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.filePath;\n                const hasFilePath = filePath && filePath !== \"N/A\";\n                const displayText = hasFilePath ? filePath : \"No file path generated\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center w-full h-full gap-2 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CopyButton, {\n                            text: hasFilePath ? filePath : \"\",\n                            disabled: !hasFilePath\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"truncate pr-2 min-w-0 flex-1 \" + (hasFilePath ? \"text-black font-mono text-xs\" : \"text-gray-500 italic text-xs\"),\n                            title: displayText,\n                            style: {\n                                minWidth: 0\n                            },\n                            children: displayText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, undefined);\n            },\n            valueGetter: (params)=>{\n                var _params_data;\n                const filePath = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.filePath;\n                if (!filePath || filePath === \"N/A\") {\n                    return \"No file path generated\";\n                }\n                return filePath;\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 550,\n            cellStyle: ()=>({\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    padding: \"4px 8px\",\n                    height: \"100%\",\n                    borderRight: \"1px solid #e0e0e0\",\n                    whiteSpace: \"nowrap\",\n                    overflow: \"hidden\",\n                    textOverflow: \"ellipsis\"\n                }),\n            tooltipValueGetter: (params)=>{\n                var _params_data;\n                const filePath = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.filePath;\n                if (!filePath || filePath === \"N/A\") {\n                    return \"No file path generated - this entry was created before file path generation was implemented\";\n                }\n                return filePath;\n            },\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"MasterInvoice\",\n            headerName: \"Master Invoice\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.masterInvoice) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"invoice\",\n            headerName: \"Invoice\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.invoice) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"bol\",\n            headerName: \"Bol\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.bol) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"receivedDate\",\n            headerName: \"Received Date\",\n            valueFormatter: (params)=>{\n                if (!params.value) return \"N/A\";\n                const dt = luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(params.value, {\n                    zone: \"utc\"\n                });\n                return dt.isValid ? dt.toFormat(\"dd-MM-yyyy\") : \"N/A\";\n            },\n            filter: \"agDateColumnFilter\",\n            filterParams: {\n                comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                    if (!cellValue) return -1;\n                    const cellDate = luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(cellValue, {\n                        zone: \"utc\"\n                    }).startOf(\"day\").toJSDate();\n                    const filterDate = new Date(Date.UTC(filterLocalDateAtMidnight.getFullYear(), filterLocalDateAtMidnight.getMonth(), filterLocalDateAtMidnight.getDate()));\n                    if (cellDate < filterDate) return -1;\n                    if (cellDate > filterDate) return 1;\n                    return 0;\n                },\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"invoiceDate\",\n            headerName: \"Invoice Date\",\n            valueGetter: (params)=>{\n                var _params_data;\n                const date = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.invoiceDate;\n                return date ? luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(date, {\n                    zone: \"utc\"\n                }).toFormat(\"dd-MM-yyyy\") : \"N/A\";\n            },\n            filter: \"agDateColumnFilter\",\n            filterParams: {\n                comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                    if (!cellValue) return -1;\n                    const cellDate = luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(cellValue, {\n                        zone: \"utc\"\n                    }).startOf(\"day\").toJSDate();\n                    const filterDate = new Date(Date.UTC(filterLocalDateAtMidnight.getFullYear(), filterLocalDateAtMidnight.getMonth(), filterLocalDateAtMidnight.getDate()));\n                    if (cellDate < filterDate) return -1;\n                    if (cellDate > filterDate) return 1;\n                    return 0;\n                },\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"shipmentDate\",\n            headerName: \"Shipment Date\",\n            valueGetter: (params)=>{\n                var _params_data;\n                const date = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.shipmentDate;\n                return date ? luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(date, {\n                    zone: \"utc\"\n                }).toFormat(\"dd-MM-yyyy\") : \"N/A\";\n            },\n            filter: \"agDateColumnFilter\",\n            filterParams: {\n                comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                    if (!cellValue) return -1;\n                    const cellDate = luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(cellValue, {\n                        zone: \"utc\"\n                    }).startOf(\"day\").toJSDate();\n                    const filterDate = new Date(Date.UTC(filterLocalDateAtMidnight.getFullYear(), filterLocalDateAtMidnight.getMonth(), filterLocalDateAtMidnight.getDate()));\n                    if (cellDate < filterDate) return -1;\n                    if (cellDate > filterDate) return 1;\n                    return 0;\n                },\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"invoiceTotal\",\n            headerName: \"Invoice Total\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.invoiceTotal) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"currency\",\n            headerName: \"Currency\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.currency) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"qtyShipped\",\n            headerName: \"Qty Shipped\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.qtyShipped) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"quantityBilledText\",\n            headerName: \"Quantity Billed\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.quantityBilledText) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"invoiceStatus\",\n            headerName: \"Invoice Status\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.invoiceStatus) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"manualMatching\",\n            headerName: \"Manual Matching\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.manualMatching) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"invoiceType\",\n            headerName: \"Invoice Type\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.invoiceType) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"weightUnitName\",\n            headerName: \"Weight Unit\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.weightUnitName) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"savings\",\n            headerName: \"Savings\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.savings) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"freightClass\",\n            headerName: \"Freight Class\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.freightClass) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"billToClient\",\n            headerName: \"Bill To Client\",\n            valueGetter: (params)=>{\n                var _params_data;\n                const billToClient = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.billToClient;\n                if (billToClient === true) return \"Yes\";\n                if (billToClient === false) return \"No\";\n                return \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"docAvailable\",\n            headerName: \"Doc Available\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.docAvailable) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"notes\",\n            headerName: \"Notes\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.notes) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            field: \"enteredBy\",\n            headerName: \"Entered by\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.enteredBy) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        }\n    ];\n    if (customFieldsMap && Object.keys(customFieldsMap).length > 0) {\n        Object.keys(customFieldsMap).forEach((fieldId)=>{\n            const fieldMeta = customFieldsMap[fieldId];\n            const fieldName = (fieldMeta === null || fieldMeta === void 0 ? void 0 : fieldMeta.name) || \"Custom Field \".concat(fieldId);\n            const fieldType = fieldMeta === null || fieldMeta === void 0 ? void 0 : fieldMeta.type;\n            baseColumns.push({\n                field: \"customField_\".concat(fieldId),\n                headerName: fieldName,\n                valueGetter: (params)=>{\n                    var _params_data_customFields, _params_data;\n                    const value = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_customFields = _params_data.customFields) === null || _params_data_customFields === void 0 ? void 0 : _params_data_customFields[fieldId];\n                    if (!value) return \"N/A\";\n                    if (fieldType === \"DATE\") {\n                        const dt = luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(value, {\n                            zone: \"utc\"\n                        });\n                        return dt.isValid ? dt.toFormat(\"dd-MM-yyyy\") : value;\n                    }\n                    return value;\n                },\n                filter: \"agTextColumnFilter\",\n                filterParams: {\n                    buttons: [\n                        \"clear\"\n                    ]\n                },\n                width: 140,\n                headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n            });\n        });\n    }\n    // Add manifest fields after custom fields\n    if (showOrcaColumns) {\n        baseColumns.push({\n            field: \"manifestStatus\",\n            headerName: \"ORCA STATUS\",\n            valueGetter: (params)=>{\n                var _params_data_manifestDetails, _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_manifestDetails = _params_data.manifestDetails) === null || _params_data_manifestDetails === void 0 ? void 0 : _params_data_manifestDetails.manifestStatus) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        }, {\n            field: \"manifestDate\",\n            headerName: \"REVIEW DATE\",\n            valueGetter: (params)=>{\n                var _params_data_manifestDetails, _params_data;\n                const date = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_manifestDetails = _params_data.manifestDetails) === null || _params_data_manifestDetails === void 0 ? void 0 : _params_data_manifestDetails.manifestDate;\n                return date ? luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(date, {\n                    zone: \"utc\"\n                }).toFormat(\"dd-MM-yyyy\") : \"N/A\";\n            },\n            filter: \"agDateColumnFilter\",\n            filterParams: {\n                comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                    if (!cellValue) return -1;\n                    const cellDate = luxon__WEBPACK_IMPORTED_MODULE_1__.DateTime.fromISO(cellValue, {\n                        zone: \"utc\"\n                    }).startOf(\"day\").toJSDate();\n                    const filterDate = new Date(Date.UTC(filterLocalDateAtMidnight.getFullYear(), filterLocalDateAtMidnight.getMonth(), filterLocalDateAtMidnight.getDate()));\n                    if (cellDate < filterDate) return -1;\n                    if (cellDate > filterDate) return 1;\n                    return 0;\n                },\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        }, {\n            field: \"actionRequired\",\n            headerName: \"ACTION REQUIRED FROM\",\n            valueGetter: (params)=>{\n                var _params_data_manifestDetails, _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_manifestDetails = _params_data.manifestDetails) === null || _params_data_manifestDetails === void 0 ? void 0 : _params_data_manifestDetails.actionRequired) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        }, {\n            field: \"manifestNotes\",\n            headerName: \"ORCA COMMENTS\",\n            valueGetter: (params)=>{\n                var _params_data_manifestDetails, _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_manifestDetails = _params_data.manifestDetails) === null || _params_data_manifestDetails === void 0 ? void 0 : _params_data_manifestDetails.manifestNotes) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 180,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        });\n    }\n    if (showLegrandColumns) {\n        baseColumns.push({\n            field: \"freightTerm\",\n            headerName: \"FREIGHT TERM\",\n            valueGetter: (params)=>{\n                var _params_data;\n                return ((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.freightTerm) || \"N/A\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            width: 140,\n            headerComponent: _app_component_PinnedHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        });\n    }\n    // Add action column after custom fields\n    baseColumns.push({\n        field: \"warnings\",\n        headerName: \"Warnings\",\n        cellRenderer: (params)=>{\n            var _params_data;\n            const warnings = (params === null || params === void 0 ? void 0 : (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.systemGeneratedWarnings) || [];\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WarningIconWithPopover, {\n                warnings: warnings\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                lineNumber: 733,\n                columnNumber: 14\n            }, undefined);\n        },\n        width: 100,\n        sortable: false,\n        cellStyle: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\"\n        }\n    });\n    baseColumns.push({\n        field: \"action\",\n        headerName: \"Action\",\n        cellRenderer: (params)=>{\n            const TrackSheet = params === null || params === void 0 ? void 0 : params.data;\n            const warnings = (TrackSheet === null || TrackSheet === void 0 ? void 0 : TrackSheet.systemGeneratedWarnings) || [];\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center gap-2\",\n                children: [\n                    showOrcaColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ManifestActionButton, {\n                        TrackSheet: TrackSheet,\n                        userData: userData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                        lineNumber: 751,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UpdateTrackSheet__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        trackSheet: TrackSheet,\n                        clientDataUpdate: clientDataUpdate,\n                        carrierDataUpdate: carrierDataUpdate\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                        lineNumber: 753,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_permissionWrapper__WEBPACK_IMPORTED_MODULE_9__.PermissionWrapper, {\n                        permissions: permissions,\n                        requiredPermissions: [\n                            \"delete-trackSheet\"\n                        ],\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_DeleteRow__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            route: \"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.DELETE_TRACK_SHEETS, \"/\").concat(TrackSheet === null || TrackSheet === void 0 ? void 0 : TrackSheet.id),\n                            onSuccess: ()=>setDeletedData(!deleteData)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\column.tsx\",\n                lineNumber: 748,\n                columnNumber: 9\n            }, undefined);\n        },\n        sortable: false,\n        width: 100,\n        pinned: \"right\",\n        cellStyle: ()=>({\n                fontFamily: \"inherit\",\n                textOverflow: \"clip\",\n                color: \"inherit\",\n                fontStyle: \"normal\"\n            })\n    });\n    return baseColumns;\n};\n_c3 = Column;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Column); /* eslint-disable */ \nfunction oo_cm() {\n    try {\n        return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x2521ed=_0x22d4;(function(_0x15e824,_0x52e4de){var _0x3fcd51=_0x22d4,_0x1510e4=_0x15e824();while(!![]){try{var _0x33050d=parseInt(_0x3fcd51(0x127))/0x1*(parseInt(_0x3fcd51(0xb5))/0x2)+parseInt(_0x3fcd51(0x11c))/0x3*(-parseInt(_0x3fcd51(0x180))/0x4)+parseInt(_0x3fcd51(0xd2))/0x5+parseInt(_0x3fcd51(0x151))/0x6*(-parseInt(_0x3fcd51(0x164))/0x7)+parseInt(_0x3fcd51(0x161))/0x8+-parseInt(_0x3fcd51(0xd3))/0x9*(parseInt(_0x3fcd51(0x108))/0xa)+parseInt(_0x3fcd51(0x129))/0xb;if(_0x33050d===_0x52e4de)break;else _0x1510e4['push'](_0x1510e4['shift']());}catch(_0x585a27){_0x1510e4['push'](_0x1510e4['shift']());}}}(_0x1bae,0xde4fd));var G=Object[_0x2521ed(0x177)],V=Object['defineProperty'],ee=Object[_0x2521ed(0xd8)],te=Object[_0x2521ed(0x15e)],ne=Object['getPrototypeOf'],re=Object['prototype'][_0x2521ed(0x94)],ie=(_0x15a493,_0x1da4e5,_0x38e998,_0x191789)=>{var _0x5aa4da=_0x2521ed;if(_0x1da4e5&&typeof _0x1da4e5==_0x5aa4da(0x110)||typeof _0x1da4e5==_0x5aa4da(0x144)){for(let _0x1898c9 of te(_0x1da4e5))!re[_0x5aa4da(0xe1)](_0x15a493,_0x1898c9)&&_0x1898c9!==_0x38e998&&V(_0x15a493,_0x1898c9,{'get':()=>_0x1da4e5[_0x1898c9],'enumerable':!(_0x191789=ee(_0x1da4e5,_0x1898c9))||_0x191789[_0x5aa4da(0xa8)]});}return _0x15a493;},j=(_0x34eccf,_0x3d547c,_0x129665)=>(_0x129665=_0x34eccf!=null?G(ne(_0x34eccf)):{},ie(_0x3d547c||!_0x34eccf||!_0x34eccf[_0x2521ed(0x160)]?V(_0x129665,_0x2521ed(0x106),{'value':_0x34eccf,'enumerable':!0x0}):_0x129665,_0x34eccf)),q=class{constructor(_0x361ce0,_0xcdc76,_0x1861eb,_0x135c38,_0x56d085,_0x24e2d1){var _0x4a32cf=_0x2521ed,_0x36205b,_0x176b2e,_0x1e4531,_0x247653;this[_0x4a32cf(0x167)]=_0x361ce0,this[_0x4a32cf(0xea)]=_0xcdc76,this[_0x4a32cf(0x189)]=_0x1861eb,this[_0x4a32cf(0x13b)]=_0x135c38,this[_0x4a32cf(0x9d)]=_0x56d085,this[_0x4a32cf(0x14f)]=_0x24e2d1,this[_0x4a32cf(0x18c)]=!0x0,this[_0x4a32cf(0x185)]=!0x0,this['_connected']=!0x1,this['_connecting']=!0x1,this[_0x4a32cf(0xf1)]=((_0x176b2e=(_0x36205b=_0x361ce0[_0x4a32cf(0x150)])==null?void 0x0:_0x36205b[_0x4a32cf(0x113)])==null?void 0x0:_0x176b2e['NEXT_RUNTIME'])===_0x4a32cf(0x107),this[_0x4a32cf(0x114)]=!((_0x247653=(_0x1e4531=this[_0x4a32cf(0x167)][_0x4a32cf(0x150)])==null?void 0x0:_0x1e4531['versions'])!=null&&_0x247653[_0x4a32cf(0x182)])&&!this[_0x4a32cf(0xf1)],this[_0x4a32cf(0x13c)]=null,this['_connectAttemptCount']=0x0,this['_maxConnectAttemptCount']=0x14,this[_0x4a32cf(0x138)]=_0x4a32cf(0x90),this[_0x4a32cf(0x17d)]=(this[_0x4a32cf(0x114)]?_0x4a32cf(0xcb):_0x4a32cf(0x13f))+this[_0x4a32cf(0x138)];}async['getWebSocketClass'](){var _0x3c3a4a=_0x2521ed,_0x3f7c0f,_0x407d5a;if(this[_0x3c3a4a(0x13c)])return this[_0x3c3a4a(0x13c)];let _0x261630;if(this['_inBrowser']||this[_0x3c3a4a(0xf1)])_0x261630=this[_0x3c3a4a(0x167)][_0x3c3a4a(0xe7)];else{if((_0x3f7c0f=this[_0x3c3a4a(0x167)][_0x3c3a4a(0x150)])!=null&&_0x3f7c0f[_0x3c3a4a(0x11a)])_0x261630=(_0x407d5a=this['global']['process'])==null?void 0x0:_0x407d5a[_0x3c3a4a(0x11a)];else try{let _0x1bc4c6=await import(_0x3c3a4a(0x16c));_0x261630=(await import((await import(_0x3c3a4a(0xdf)))[_0x3c3a4a(0xc4)](_0x1bc4c6[_0x3c3a4a(0x148)](this[_0x3c3a4a(0x13b)],_0x3c3a4a(0x12f)))[_0x3c3a4a(0x12a)]()))[_0x3c3a4a(0x106)];}catch{try{_0x261630=require(require('path')[_0x3c3a4a(0x148)](this[_0x3c3a4a(0x13b)],'ws'));}catch{throw new Error(_0x3c3a4a(0xa0));}}}return this['_WebSocketClass']=_0x261630,_0x261630;}['_connectToHostNow'](){var _0x24affd=_0x2521ed;this[_0x24affd(0xd4)]||this[_0x24affd(0xb3)]||this[_0x24affd(0x166)]>=this[_0x24affd(0xb2)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x24affd(0x166)]++,this[_0x24affd(0xaa)]=new Promise((_0x1c13ba,_0x542a06)=>{var _0x3c2948=_0x24affd;this[_0x3c2948(0x16d)]()[_0x3c2948(0x121)](_0x14d8ea=>{var _0x93c003=_0x3c2948;let _0x375890=new _0x14d8ea(_0x93c003(0x12b)+(!this[_0x93c003(0x114)]&&this[_0x93c003(0x9d)]?_0x93c003(0x18d):this[_0x93c003(0xea)])+':'+this[_0x93c003(0x189)]);_0x375890[_0x93c003(0x128)]=()=>{var _0x5756af=_0x93c003;this['_allowedToSend']=!0x1,this[_0x5756af(0x171)](_0x375890),this[_0x5756af(0x10c)](),_0x542a06(new Error('logger\\\\x20websocket\\\\x20error'));},_0x375890[_0x93c003(0xc6)]=()=>{var _0x49cc48=_0x93c003;this[_0x49cc48(0x114)]||_0x375890[_0x49cc48(0x140)]&&_0x375890[_0x49cc48(0x140)]['unref']&&_0x375890[_0x49cc48(0x140)][_0x49cc48(0xf3)](),_0x1c13ba(_0x375890);},_0x375890[_0x93c003(0x187)]=()=>{var _0x23463a=_0x93c003;this[_0x23463a(0x185)]=!0x0,this[_0x23463a(0x171)](_0x375890),this['_attemptToReconnectShortly']();},_0x375890[_0x93c003(0xc9)]=_0x75a52e=>{var _0x3951ee=_0x93c003;try{if(!(_0x75a52e!=null&&_0x75a52e[_0x3951ee(0xa3)])||!this[_0x3951ee(0x14f)])return;let _0x78d515=JSON[_0x3951ee(0x111)](_0x75a52e[_0x3951ee(0xa3)]);this[_0x3951ee(0x14f)](_0x78d515[_0x3951ee(0xec)],_0x78d515[_0x3951ee(0x157)],this['global'],this[_0x3951ee(0x114)]);}catch{}};})[_0x3c2948(0x121)](_0x51dfb1=>(this['_connected']=!0x0,this[_0x3c2948(0xd4)]=!0x1,this[_0x3c2948(0x185)]=!0x1,this[_0x3c2948(0x18c)]=!0x0,this['_connectAttemptCount']=0x0,_0x51dfb1))[_0x3c2948(0xc0)](_0xb4565c=>(this[_0x3c2948(0xb3)]=!0x1,this[_0x3c2948(0xd4)]=!0x1,console[_0x3c2948(0x184)](_0x3c2948(0xb4)+this[_0x3c2948(0x138)]),_0x542a06(new Error(_0x3c2948(0xdc)+(_0xb4565c&&_0xb4565c['message'])))));}));}[_0x2521ed(0x171)](_0x2bbff1){var _0x7a7224=_0x2521ed;this[_0x7a7224(0xb3)]=!0x1,this[_0x7a7224(0xd4)]=!0x1;try{_0x2bbff1['onclose']=null,_0x2bbff1[_0x7a7224(0x128)]=null,_0x2bbff1['onopen']=null;}catch{}try{_0x2bbff1[_0x7a7224(0xc2)]<0x2&&_0x2bbff1[_0x7a7224(0x116)]();}catch{}}[_0x2521ed(0x10c)](){var _0x822240=_0x2521ed;clearTimeout(this[_0x822240(0x181)]),!(this[_0x822240(0x166)]>=this[_0x822240(0xb2)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0x572ab4=_0x822240,_0x59a299;this[_0x572ab4(0xb3)]||this['_connecting']||(this[_0x572ab4(0x16e)](),(_0x59a299=this[_0x572ab4(0xaa)])==null||_0x59a299[_0x572ab4(0xc0)](()=>this[_0x572ab4(0x10c)]()));},0x1f4),this[_0x822240(0x181)]['unref']&&this[_0x822240(0x181)][_0x822240(0xf3)]());}async[_0x2521ed(0xbd)](_0x4b15cd){var _0x362d6e=_0x2521ed;try{if(!this['_allowedToSend'])return;this[_0x362d6e(0x185)]&&this[_0x362d6e(0x16e)](),(await this['_ws'])[_0x362d6e(0xbd)](JSON[_0x362d6e(0xd5)](_0x4b15cd));}catch(_0x152473){this[_0x362d6e(0xbf)]?console[_0x362d6e(0x184)](this[_0x362d6e(0x17d)]+':\\\\x20'+(_0x152473&&_0x152473['message'])):(this[_0x362d6e(0xbf)]=!0x0,console[_0x362d6e(0x184)](this['_sendErrorMessage']+':\\\\x20'+(_0x152473&&_0x152473[_0x362d6e(0x12d)]),_0x4b15cd)),this['_allowedToSend']=!0x1,this[_0x362d6e(0x10c)]();}}};function _0x1bae(){var _0x475517=['unknown','_addFunctionsNode','failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket','replace','parent','data','[object\\\\x20Set]','autoExpandPreviousObjects','_setNodeExpressionPath','value','enumerable','autoExpandPropertyCount','_ws','_setNodeQueryPath','next.js','trace','_p_length','depth','56008','push','_maxConnectAttemptCount','_connected','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','163798kPEYdI','expressionsToEvaluate','string','current','noFunctions','_addProperty','_additionalMetadata','funcName','send','_p_name','_extendedWarning','catch','_consoleNinjaAllowedToStart','readyState',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DESKTOP-QUQRE9R\\\",\\\"***************\\\"],'pathToFileURL','bigint','onopen','Boolean','_treeNodePropertiesBeforeFullValue','onmessage','[object\\\\x20Array]','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','console','elements','_getOwnPropertySymbols','totalStrLength','_isArray','Map','7861370iASdlg','2079585kwXuMV','_connecting','stringify','_propertyName','log','getOwnPropertyDescriptor','RegExp','_treeNodePropertiesAfterFullValue','resolveGetters','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','hrtime','props','url','level','call','map','_isPrimitiveWrapperType','_blacklistedProperty','[object\\\\x20BigInt]','match','WebSocket','serialize','autoExpandMaxDepth','host','strLength','method','_isMap','Number','unshift','versions','_inNextEdge','_Symbol','unref','_isPrimitiveType','length','_numberRegExp','_setNodeId','_console_ninja','constructor','_console_ninja_session','rootExpression',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.cursor\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.460-universal\\\\\\\\node_modules\\\",'angular','null','slice','Symbol','bind','index','array','pop','boolean','default','edge','10jggNHF','astro','_p_','NEXT_RUNTIME','_attemptToReconnectShortly','capped','','127.0.0.1','object','parse','_setNodeExpandableState','env','_inBrowser','allStrLength','close','remix','_isNegativeZero','disabledLog','_WebSocket','origin','24jBCuxm','date','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','count','timeStamp','then','_addObjectProperty','NEGATIVE_INFINITY','_addLoadNode','nan','symbol','1lYcQod','onerror','13530121SEjCVL','toString','ws://','next.js','message','location','ws/index.js','_regExpToString','setter','[object\\\\x20Map]','_keyStrRegExp','isExpressionToEvaluate','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','Error','time','_webSocketErrorDocsLink','includes','root_exp','nodeModules','_WebSocketClass','get','coverage','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_socket','prototype','reduceLimits','_dateToString','function','_isSet','String','_objectToString','join','_hasMapOnItsPath','_setNodeLabel','reload','substr','endsWith','autoExpandLimit','eventReceivedCallback','process','4201998ehWVyE','performance','stackTraceLimit','test','set','autoExpand','args','perf_hooks','_getOwnPropertyDescriptor','_capIfString','_getOwnPropertyNames','valueOf','now','getOwnPropertyNames','...','__es'+'Module','2697880RnkQZv','type','stack','7ePNIPb','fromCharCode','_connectAttemptCount','global','negativeZero','_ninjaIgnoreNextError','error','_sortProps','path','getWebSocketClass','_connectToHostNow','_setNodePermissions','_undefined','_disposeWebsocket','cappedProps','negativeInfinity','sortProps','HTMLAllCollection','indexOf','create','_hasSymbolPropertyOnItsPath','charAt','hits','name','_cleanNode','_sendErrorMessage','_property','_type','689716KYriyM','_reconnectTimeout','node','getter','warn','_allowedToConnectOnSend','Set','onclose','number','port','root_exp_id','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','disabledTrace','undefined','https://tinyurl.com/37x8b79t','expId','forEach','_processTreeNodeResult','hasOwnProperty','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','POSITIVE_INFINITY','isArray','[object\\\\x20Date]','_HTMLAllCollection','concat','hostname','\\\\x20browser','dockerizedApp'];_0x1bae=function(){return _0x475517;};return _0x1bae();}function H(_0x5ac5d3,_0x5a024c,_0x41428f,_0x2b7c5f,_0x32bd1c,_0x410870,_0x173eb4,_0x411518=oe){var _0x35ca23=_0x2521ed;let _0x2973b8=_0x41428f['split'](',')['map'](_0x4ce34d=>{var _0x54e87f=_0x22d4,_0x4bc88c,_0x1896c3,_0x4bf3fc,_0x1b5e14;try{if(!_0x5ac5d3[_0x54e87f(0xfa)]){let _0x2f2f7f=((_0x1896c3=(_0x4bc88c=_0x5ac5d3[_0x54e87f(0x150)])==null?void 0x0:_0x4bc88c[_0x54e87f(0xf0)])==null?void 0x0:_0x1896c3['node'])||((_0x1b5e14=(_0x4bf3fc=_0x5ac5d3[_0x54e87f(0x150)])==null?void 0x0:_0x4bf3fc['env'])==null?void 0x0:_0x1b5e14[_0x54e87f(0x10b)])==='edge';(_0x32bd1c===_0x54e87f(0xac)||_0x32bd1c===_0x54e87f(0x117)||_0x32bd1c===_0x54e87f(0x109)||_0x32bd1c===_0x54e87f(0xfd))&&(_0x32bd1c+=_0x2f2f7f?'\\\\x20server':_0x54e87f(0x9c)),_0x5ac5d3[_0x54e87f(0xfa)]={'id':+new Date(),'tool':_0x32bd1c},_0x173eb4&&_0x32bd1c&&!_0x2f2f7f&&console[_0x54e87f(0xd7)](_0x54e87f(0x135)+(_0x32bd1c[_0x54e87f(0x179)](0x0)['toUpperCase']()+_0x32bd1c[_0x54e87f(0x14c)](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x54e87f(0x11e));}let _0x38266e=new q(_0x5ac5d3,_0x5a024c,_0x4ce34d,_0x2b7c5f,_0x410870,_0x411518);return _0x38266e[_0x54e87f(0xbd)][_0x54e87f(0x101)](_0x38266e);}catch(_0x605813){return console[_0x54e87f(0x184)](_0x54e87f(0x95),_0x605813&&_0x605813[_0x54e87f(0x12d)]),()=>{};}});return _0x2cbe92=>_0x2973b8[_0x35ca23(0x92)](_0x4df820=>_0x4df820(_0x2cbe92));}function oe(_0x4723a9,_0xdcafa3,_0x18cb5f,_0x306248){var _0x7974a6=_0x2521ed;_0x306248&&_0x4723a9===_0x7974a6(0x14b)&&_0x18cb5f[_0x7974a6(0x12e)][_0x7974a6(0x14b)]();}function B(_0x49df36){var _0x8c4455=_0x2521ed,_0x1aef62,_0x33ca0f;let _0x504d4d=function(_0x3e4d21,_0x3119a9){return _0x3119a9-_0x3e4d21;},_0x4e68dc;if(_0x49df36[_0x8c4455(0x152)])_0x4e68dc=function(){return _0x49df36['performance']['now']();};else{if(_0x49df36[_0x8c4455(0x150)]&&_0x49df36[_0x8c4455(0x150)]['hrtime']&&((_0x33ca0f=(_0x1aef62=_0x49df36['process'])==null?void 0x0:_0x1aef62[_0x8c4455(0x113)])==null?void 0x0:_0x33ca0f[_0x8c4455(0x10b)])!==_0x8c4455(0x107))_0x4e68dc=function(){var _0x1f5058=_0x8c4455;return _0x49df36[_0x1f5058(0x150)][_0x1f5058(0xdd)]();},_0x504d4d=function(_0x4e233c,_0x163bff){return 0x3e8*(_0x163bff[0x0]-_0x4e233c[0x0])+(_0x163bff[0x1]-_0x4e233c[0x1])/0xf4240;};else try{let {performance:_0x92d690}=require(_0x8c4455(0x158));_0x4e68dc=function(){return _0x92d690['now']();};}catch{_0x4e68dc=function(){return+new Date();};}}return{'elapsed':_0x504d4d,'timeStamp':_0x4e68dc,'now':()=>Date[_0x8c4455(0x15d)]()};}function _0x22d4(_0x12edb4,_0x271789){var _0x1bae16=_0x1bae();return _0x22d4=function(_0x22d4de,_0x5d435d){_0x22d4de=_0x22d4de-0x8f;var _0x5cf399=_0x1bae16[_0x22d4de];return _0x5cf399;},_0x22d4(_0x12edb4,_0x271789);}function X(_0x353b15,_0x1aa730,_0x1de36e){var _0x290fd=_0x2521ed,_0x2cd4ae,_0x424619,_0x4f2a6c,_0x1d9986,_0x230056;if(_0x353b15[_0x290fd(0xc1)]!==void 0x0)return _0x353b15[_0x290fd(0xc1)];let _0x542a02=((_0x424619=(_0x2cd4ae=_0x353b15['process'])==null?void 0x0:_0x2cd4ae[_0x290fd(0xf0)])==null?void 0x0:_0x424619[_0x290fd(0x182)])||((_0x1d9986=(_0x4f2a6c=_0x353b15[_0x290fd(0x150)])==null?void 0x0:_0x4f2a6c[_0x290fd(0x113)])==null?void 0x0:_0x1d9986[_0x290fd(0x10b)])==='edge';function _0x394a2b(_0x45bd35){var _0x5eb749=_0x290fd;if(_0x45bd35['startsWith']('/')&&_0x45bd35[_0x5eb749(0x14d)]('/')){let _0x21a10f=new RegExp(_0x45bd35[_0x5eb749(0xff)](0x1,-0x1));return _0xe0fca5=>_0x21a10f[_0x5eb749(0x154)](_0xe0fca5);}else{if(_0x45bd35[_0x5eb749(0x139)]('*')||_0x45bd35[_0x5eb749(0x139)]('?')){let _0x95c811=new RegExp('^'+_0x45bd35['replace'](/\\\\./g,String[_0x5eb749(0x165)](0x5c)+'.')['replace'](/\\\\*/g,'.*')['replace'](/\\\\?/g,'.')+String[_0x5eb749(0x165)](0x24));return _0x40ec94=>_0x95c811[_0x5eb749(0x154)](_0x40ec94);}else return _0x3f24dd=>_0x3f24dd===_0x45bd35;}}let _0x2ca7df=_0x1aa730[_0x290fd(0xe2)](_0x394a2b);return _0x353b15[_0x290fd(0xc1)]=_0x542a02||!_0x1aa730,!_0x353b15[_0x290fd(0xc1)]&&((_0x230056=_0x353b15[_0x290fd(0x12e)])==null?void 0x0:_0x230056['hostname'])&&(_0x353b15[_0x290fd(0xc1)]=_0x2ca7df['some'](_0x23fe2b=>_0x23fe2b(_0x353b15[_0x290fd(0x12e)][_0x290fd(0x9b)]))),_0x353b15['_consoleNinjaAllowedToStart'];}function J(_0x1c6eb0,_0x552919,_0x51de18,_0x533c9c){var _0xd5a59d=_0x2521ed;_0x1c6eb0=_0x1c6eb0,_0x552919=_0x552919,_0x51de18=_0x51de18,_0x533c9c=_0x533c9c;let _0x1136ef=B(_0x1c6eb0),_0x1a1b80=_0x1136ef['elapsed'],_0x57d945=_0x1136ef[_0xd5a59d(0x120)];class _0x4a2137{constructor(){var _0x20a872=_0xd5a59d;this[_0x20a872(0x133)]=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x20a872(0xf6)]=/^(0|[1-9][0-9]*)$/,this['_quotedRegExp']=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x20a872(0x170)]=_0x1c6eb0[_0x20a872(0x8f)],this[_0x20a872(0x99)]=_0x1c6eb0['HTMLAllCollection'],this[_0x20a872(0x159)]=Object[_0x20a872(0xd8)],this[_0x20a872(0x15b)]=Object[_0x20a872(0x15e)],this[_0x20a872(0xf2)]=_0x1c6eb0[_0x20a872(0x100)],this[_0x20a872(0x130)]=RegExp[_0x20a872(0x141)][_0x20a872(0x12a)],this['_dateToString']=Date[_0x20a872(0x141)][_0x20a872(0x12a)];}[_0xd5a59d(0xe8)](_0x19e7d2,_0xdeff97,_0x1b41e0,_0x5a8d18){var _0x369a40=_0xd5a59d,_0x4e0a0b=this,_0x207418=_0x1b41e0[_0x369a40(0x156)];function _0x356ab7(_0x1764a0,_0x28cb69,_0x5cd086){var _0x2e85e4=_0x369a40;_0x28cb69[_0x2e85e4(0x162)]=_0x2e85e4(0x9e),_0x28cb69[_0x2e85e4(0x16a)]=_0x1764a0[_0x2e85e4(0x12d)],_0x1f9073=_0x5cd086[_0x2e85e4(0x182)][_0x2e85e4(0xb8)],_0x5cd086[_0x2e85e4(0x182)][_0x2e85e4(0xb8)]=_0x28cb69,_0x4e0a0b[_0x2e85e4(0xc8)](_0x28cb69,_0x5cd086);}let _0x371ae5;_0x1c6eb0[_0x369a40(0xcc)]&&(_0x371ae5=_0x1c6eb0[_0x369a40(0xcc)][_0x369a40(0x16a)],_0x371ae5&&(_0x1c6eb0['console'][_0x369a40(0x16a)]=function(){}));try{try{_0x1b41e0['level']++,_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0['autoExpandPreviousObjects'][_0x369a40(0xb1)](_0xdeff97);var _0x1b2cf8,_0x5634a8,_0x24aca3,_0x524893,_0x2a7853=[],_0x3725f9=[],_0x1b95d6,_0x5f3857=this[_0x369a40(0x17f)](_0xdeff97),_0x40dc89=_0x5f3857===_0x369a40(0x103),_0x3a0f81=!0x1,_0x3e6606=_0x5f3857===_0x369a40(0x144),_0x12c80b=this[_0x369a40(0xf4)](_0x5f3857),_0x400804=this[_0x369a40(0xe3)](_0x5f3857),_0x4a9776=_0x12c80b||_0x400804,_0x5335ea={},_0x3502e7=0x0,_0x2885e3=!0x1,_0x1f9073,_0x318e18=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1b41e0[_0x369a40(0xaf)]){if(_0x40dc89){if(_0x5634a8=_0xdeff97[_0x369a40(0xf5)],_0x5634a8>_0x1b41e0['elements']){for(_0x24aca3=0x0,_0x524893=_0x1b41e0['elements'],_0x1b2cf8=_0x24aca3;_0x1b2cf8<_0x524893;_0x1b2cf8++)_0x3725f9['push'](_0x4e0a0b[_0x369a40(0xba)](_0x2a7853,_0xdeff97,_0x5f3857,_0x1b2cf8,_0x1b41e0));_0x19e7d2['cappedElements']=!0x0;}else{for(_0x24aca3=0x0,_0x524893=_0x5634a8,_0x1b2cf8=_0x24aca3;_0x1b2cf8<_0x524893;_0x1b2cf8++)_0x3725f9[_0x369a40(0xb1)](_0x4e0a0b[_0x369a40(0xba)](_0x2a7853,_0xdeff97,_0x5f3857,_0x1b2cf8,_0x1b41e0));}_0x1b41e0['autoExpandPropertyCount']+=_0x3725f9[_0x369a40(0xf5)];}if(!(_0x5f3857==='null'||_0x5f3857===_0x369a40(0x8f))&&!_0x12c80b&&_0x5f3857!==_0x369a40(0x146)&&_0x5f3857!=='Buffer'&&_0x5f3857!=='bigint'){var _0x9b164d=_0x5a8d18[_0x369a40(0xde)]||_0x1b41e0[_0x369a40(0xde)];if(this['_isSet'](_0xdeff97)?(_0x1b2cf8=0x0,_0xdeff97[_0x369a40(0x92)](function(_0x2e4c83){var _0xc34cf5=_0x369a40;if(_0x3502e7++,_0x1b41e0['autoExpandPropertyCount']++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;return;}if(!_0x1b41e0['isExpressionToEvaluate']&&_0x1b41e0[_0xc34cf5(0x156)]&&_0x1b41e0[_0xc34cf5(0xa9)]>_0x1b41e0[_0xc34cf5(0x14e)]){_0x2885e3=!0x0;return;}_0x3725f9[_0xc34cf5(0xb1)](_0x4e0a0b[_0xc34cf5(0xba)](_0x2a7853,_0xdeff97,_0xc34cf5(0x186),_0x1b2cf8++,_0x1b41e0,function(_0x29d796){return function(){return _0x29d796;};}(_0x2e4c83)));})):this[_0x369a40(0xed)](_0xdeff97)&&_0xdeff97[_0x369a40(0x92)](function(_0xfea0fe,_0xa14803){var _0x32aa72=_0x369a40;if(_0x3502e7++,_0x1b41e0[_0x32aa72(0xa9)]++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;return;}if(!_0x1b41e0['isExpressionToEvaluate']&&_0x1b41e0[_0x32aa72(0x156)]&&_0x1b41e0['autoExpandPropertyCount']>_0x1b41e0['autoExpandLimit']){_0x2885e3=!0x0;return;}var _0x3e871d=_0xa14803['toString']();_0x3e871d['length']>0x64&&(_0x3e871d=_0x3e871d[_0x32aa72(0xff)](0x0,0x64)+_0x32aa72(0x15f)),_0x3725f9[_0x32aa72(0xb1)](_0x4e0a0b[_0x32aa72(0xba)](_0x2a7853,_0xdeff97,_0x32aa72(0xd1),_0x3e871d,_0x1b41e0,function(_0x4b9377){return function(){return _0x4b9377;};}(_0xfea0fe)));}),!_0x3a0f81){try{for(_0x1b95d6 in _0xdeff97)if(!(_0x40dc89&&_0x318e18[_0x369a40(0x154)](_0x1b95d6))&&!this[_0x369a40(0xe4)](_0xdeff97,_0x1b95d6,_0x1b41e0)){if(_0x3502e7++,_0x1b41e0[_0x369a40(0xa9)]++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;break;}if(!_0x1b41e0[_0x369a40(0x134)]&&_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0[_0x369a40(0xa9)]>_0x1b41e0['autoExpandLimit']){_0x2885e3=!0x0;break;}_0x3725f9[_0x369a40(0xb1)](_0x4e0a0b['_addObjectProperty'](_0x2a7853,_0x5335ea,_0xdeff97,_0x5f3857,_0x1b95d6,_0x1b41e0));}}catch{}if(_0x5335ea[_0x369a40(0xae)]=!0x0,_0x3e6606&&(_0x5335ea[_0x369a40(0xbe)]=!0x0),!_0x2885e3){var _0x3b6b3a=[][_0x369a40(0x9a)](this[_0x369a40(0x15b)](_0xdeff97))['concat'](this[_0x369a40(0xce)](_0xdeff97));for(_0x1b2cf8=0x0,_0x5634a8=_0x3b6b3a[_0x369a40(0xf5)];_0x1b2cf8<_0x5634a8;_0x1b2cf8++)if(_0x1b95d6=_0x3b6b3a[_0x1b2cf8],!(_0x40dc89&&_0x318e18[_0x369a40(0x154)](_0x1b95d6['toString']()))&&!this[_0x369a40(0xe4)](_0xdeff97,_0x1b95d6,_0x1b41e0)&&!_0x5335ea[_0x369a40(0x10a)+_0x1b95d6[_0x369a40(0x12a)]()]){if(_0x3502e7++,_0x1b41e0[_0x369a40(0xa9)]++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;break;}if(!_0x1b41e0[_0x369a40(0x134)]&&_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0[_0x369a40(0xa9)]>_0x1b41e0[_0x369a40(0x14e)]){_0x2885e3=!0x0;break;}_0x3725f9[_0x369a40(0xb1)](_0x4e0a0b[_0x369a40(0x122)](_0x2a7853,_0x5335ea,_0xdeff97,_0x5f3857,_0x1b95d6,_0x1b41e0));}}}}}if(_0x19e7d2['type']=_0x5f3857,_0x4a9776?(_0x19e7d2['value']=_0xdeff97[_0x369a40(0x15c)](),this[_0x369a40(0x15a)](_0x5f3857,_0x19e7d2,_0x1b41e0,_0x5a8d18)):_0x5f3857===_0x369a40(0x11d)?_0x19e7d2[_0x369a40(0xa7)]=this[_0x369a40(0x143)][_0x369a40(0xe1)](_0xdeff97):_0x5f3857===_0x369a40(0xc5)?_0x19e7d2[_0x369a40(0xa7)]=_0xdeff97[_0x369a40(0x12a)]():_0x5f3857===_0x369a40(0xd9)?_0x19e7d2[_0x369a40(0xa7)]=this[_0x369a40(0x130)]['call'](_0xdeff97):_0x5f3857==='symbol'&&this['_Symbol']?_0x19e7d2['value']=this[_0x369a40(0xf2)][_0x369a40(0x141)][_0x369a40(0x12a)][_0x369a40(0xe1)](_0xdeff97):!_0x1b41e0[_0x369a40(0xaf)]&&!(_0x5f3857===_0x369a40(0xfe)||_0x5f3857==='undefined')&&(delete _0x19e7d2[_0x369a40(0xa7)],_0x19e7d2['capped']=!0x0),_0x2885e3&&(_0x19e7d2[_0x369a40(0x172)]=!0x0),_0x1f9073=_0x1b41e0[_0x369a40(0x182)][_0x369a40(0xb8)],_0x1b41e0[_0x369a40(0x182)][_0x369a40(0xb8)]=_0x19e7d2,this['_treeNodePropertiesBeforeFullValue'](_0x19e7d2,_0x1b41e0),_0x3725f9['length']){for(_0x1b2cf8=0x0,_0x5634a8=_0x3725f9['length'];_0x1b2cf8<_0x5634a8;_0x1b2cf8++)_0x3725f9[_0x1b2cf8](_0x1b2cf8);}_0x2a7853['length']&&(_0x19e7d2['props']=_0x2a7853);}catch(_0x43255c){_0x356ab7(_0x43255c,_0x19e7d2,_0x1b41e0);}this[_0x369a40(0xbb)](_0xdeff97,_0x19e7d2),this['_treeNodePropertiesAfterFullValue'](_0x19e7d2,_0x1b41e0),_0x1b41e0[_0x369a40(0x182)][_0x369a40(0xb8)]=_0x1f9073,_0x1b41e0[_0x369a40(0xe0)]--,_0x1b41e0[_0x369a40(0x156)]=_0x207418,_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0['autoExpandPreviousObjects'][_0x369a40(0x104)]();}finally{_0x371ae5&&(_0x1c6eb0['console'][_0x369a40(0x16a)]=_0x371ae5);}return _0x19e7d2;}['_getOwnPropertySymbols'](_0x47571d){var _0x1b79d4=_0xd5a59d;return Object[_0x1b79d4(0x18b)]?Object[_0x1b79d4(0x18b)](_0x47571d):[];}[_0xd5a59d(0x145)](_0x2365a1){var _0x95c97b=_0xd5a59d;return!!(_0x2365a1&&_0x1c6eb0[_0x95c97b(0x186)]&&this[_0x95c97b(0x147)](_0x2365a1)===_0x95c97b(0xa4)&&_0x2365a1['forEach']);}[_0xd5a59d(0xe4)](_0x13f89c,_0x154bef,_0x43a2af){return _0x43a2af['noFunctions']?typeof _0x13f89c[_0x154bef]=='function':!0x1;}[_0xd5a59d(0x17f)](_0x40d886){var _0x2b5971=_0xd5a59d,_0x508ff0='';return _0x508ff0=typeof _0x40d886,_0x508ff0===_0x2b5971(0x110)?this[_0x2b5971(0x147)](_0x40d886)==='[object\\\\x20Array]'?_0x508ff0=_0x2b5971(0x103):this[_0x2b5971(0x147)](_0x40d886)===_0x2b5971(0x98)?_0x508ff0=_0x2b5971(0x11d):this['_objectToString'](_0x40d886)===_0x2b5971(0xe5)?_0x508ff0='bigint':_0x40d886===null?_0x508ff0=_0x2b5971(0xfe):_0x40d886[_0x2b5971(0xf9)]&&(_0x508ff0=_0x40d886[_0x2b5971(0xf9)]['name']||_0x508ff0):_0x508ff0===_0x2b5971(0x8f)&&this['_HTMLAllCollection']&&_0x40d886 instanceof this[_0x2b5971(0x99)]&&(_0x508ff0=_0x2b5971(0x175)),_0x508ff0;}['_objectToString'](_0x589848){var _0x2742fa=_0xd5a59d;return Object['prototype']['toString'][_0x2742fa(0xe1)](_0x589848);}[_0xd5a59d(0xf4)](_0x52f421){var _0x28f82f=_0xd5a59d;return _0x52f421===_0x28f82f(0x105)||_0x52f421===_0x28f82f(0xb7)||_0x52f421===_0x28f82f(0x188);}[_0xd5a59d(0xe3)](_0x355bbd){var _0x51962f=_0xd5a59d;return _0x355bbd===_0x51962f(0xc7)||_0x355bbd===_0x51962f(0x146)||_0x355bbd===_0x51962f(0xee);}[_0xd5a59d(0xba)](_0x9ba98c,_0x596b39,_0x3e2452,_0x923cf0,_0x1f8e7a,_0x3ccbed){var _0x4e9c48=this;return function(_0x1e84c3){var _0x25e331=_0x22d4,_0x1119fe=_0x1f8e7a['node'][_0x25e331(0xb8)],_0x3061fc=_0x1f8e7a[_0x25e331(0x182)]['index'],_0x422100=_0x1f8e7a[_0x25e331(0x182)][_0x25e331(0xa2)];_0x1f8e7a[_0x25e331(0x182)][_0x25e331(0xa2)]=_0x1119fe,_0x1f8e7a[_0x25e331(0x182)]['index']=typeof _0x923cf0==_0x25e331(0x188)?_0x923cf0:_0x1e84c3,_0x9ba98c['push'](_0x4e9c48[_0x25e331(0x17e)](_0x596b39,_0x3e2452,_0x923cf0,_0x1f8e7a,_0x3ccbed)),_0x1f8e7a['node']['parent']=_0x422100,_0x1f8e7a[_0x25e331(0x182)][_0x25e331(0x102)]=_0x3061fc;};}['_addObjectProperty'](_0xf7c578,_0x51971d,_0x33c44e,_0x528a14,_0x297309,_0x4ead46,_0x51de28){var _0x240d55=_0xd5a59d,_0x3d8a22=this;return _0x51971d['_p_'+_0x297309[_0x240d55(0x12a)]()]=!0x0,function(_0x523495){var _0x2424bf=_0x240d55,_0x5d1930=_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0xb8)],_0x36b6f9=_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0x102)],_0x242217=_0x4ead46['node']['parent'];_0x4ead46['node'][_0x2424bf(0xa2)]=_0x5d1930,_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0x102)]=_0x523495,_0xf7c578['push'](_0x3d8a22['_property'](_0x33c44e,_0x528a14,_0x297309,_0x4ead46,_0x51de28)),_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0xa2)]=_0x242217,_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0x102)]=_0x36b6f9;};}[_0xd5a59d(0x17e)](_0x2fcb0b,_0x50df32,_0x538478,_0x548d99,_0xb9e029){var _0x3006dd=_0xd5a59d,_0x20f0d9=this;_0xb9e029||(_0xb9e029=function(_0x1130b7,_0x5ae751){return _0x1130b7[_0x5ae751];});var _0x530633=_0x538478['toString'](),_0x10423d=_0x548d99[_0x3006dd(0xb6)]||{},_0x3d799d=_0x548d99[_0x3006dd(0xaf)],_0x3bdd25=_0x548d99['isExpressionToEvaluate'];try{var _0x55f627=this[_0x3006dd(0xed)](_0x2fcb0b),_0x274f99=_0x530633;_0x55f627&&_0x274f99[0x0]==='\\\\x27'&&(_0x274f99=_0x274f99[_0x3006dd(0x14c)](0x1,_0x274f99[_0x3006dd(0xf5)]-0x2));var _0x6b28ec=_0x548d99[_0x3006dd(0xb6)]=_0x10423d[_0x3006dd(0x10a)+_0x274f99];_0x6b28ec&&(_0x548d99[_0x3006dd(0xaf)]=_0x548d99['depth']+0x1),_0x548d99[_0x3006dd(0x134)]=!!_0x6b28ec;var _0x434c3=typeof _0x538478==_0x3006dd(0x126),_0xd44407={'name':_0x434c3||_0x55f627?_0x530633:this['_propertyName'](_0x530633)};if(_0x434c3&&(_0xd44407[_0x3006dd(0x126)]=!0x0),!(_0x50df32===_0x3006dd(0x103)||_0x50df32===_0x3006dd(0x136))){var _0xb21498=this[_0x3006dd(0x159)](_0x2fcb0b,_0x538478);if(_0xb21498&&(_0xb21498[_0x3006dd(0x155)]&&(_0xd44407[_0x3006dd(0x131)]=!0x0),_0xb21498[_0x3006dd(0x13d)]&&!_0x6b28ec&&!_0x548d99[_0x3006dd(0xdb)]))return _0xd44407[_0x3006dd(0x183)]=!0x0,this[_0x3006dd(0x93)](_0xd44407,_0x548d99),_0xd44407;}var _0x2d48a0;try{_0x2d48a0=_0xb9e029(_0x2fcb0b,_0x538478);}catch(_0x26552e){return _0xd44407={'name':_0x530633,'type':'unknown','error':_0x26552e[_0x3006dd(0x12d)]},this[_0x3006dd(0x93)](_0xd44407,_0x548d99),_0xd44407;}var _0x4844c2=this[_0x3006dd(0x17f)](_0x2d48a0),_0x2fcd14=this[_0x3006dd(0xf4)](_0x4844c2);if(_0xd44407[_0x3006dd(0x162)]=_0x4844c2,_0x2fcd14)this[_0x3006dd(0x93)](_0xd44407,_0x548d99,_0x2d48a0,function(){var _0x439489=_0x3006dd;_0xd44407['value']=_0x2d48a0[_0x439489(0x15c)](),!_0x6b28ec&&_0x20f0d9[_0x439489(0x15a)](_0x4844c2,_0xd44407,_0x548d99,{});});else{var _0x31d1c7=_0x548d99[_0x3006dd(0x156)]&&_0x548d99['level']<_0x548d99['autoExpandMaxDepth']&&_0x548d99[_0x3006dd(0xa5)][_0x3006dd(0x176)](_0x2d48a0)<0x0&&_0x4844c2!=='function'&&_0x548d99[_0x3006dd(0xa9)]<_0x548d99[_0x3006dd(0x14e)];_0x31d1c7||_0x548d99['level']<_0x3d799d||_0x6b28ec?(this[_0x3006dd(0xe8)](_0xd44407,_0x2d48a0,_0x548d99,_0x6b28ec||{}),this[_0x3006dd(0xbb)](_0x2d48a0,_0xd44407)):this[_0x3006dd(0x93)](_0xd44407,_0x548d99,_0x2d48a0,function(){var _0x5e1f7c=_0x3006dd;_0x4844c2===_0x5e1f7c(0xfe)||_0x4844c2===_0x5e1f7c(0x8f)||(delete _0xd44407[_0x5e1f7c(0xa7)],_0xd44407['capped']=!0x0);});}return _0xd44407;}finally{_0x548d99[_0x3006dd(0xb6)]=_0x10423d,_0x548d99[_0x3006dd(0xaf)]=_0x3d799d,_0x548d99[_0x3006dd(0x134)]=_0x3bdd25;}}['_capIfString'](_0x1e4d2e,_0x294ac4,_0x367bfb,_0x20ba06){var _0x58e966=_0xd5a59d,_0x3d317c=_0x20ba06['strLength']||_0x367bfb[_0x58e966(0xeb)];if((_0x1e4d2e===_0x58e966(0xb7)||_0x1e4d2e===_0x58e966(0x146))&&_0x294ac4['value']){let _0x5e11f9=_0x294ac4[_0x58e966(0xa7)][_0x58e966(0xf5)];_0x367bfb[_0x58e966(0x115)]+=_0x5e11f9,_0x367bfb[_0x58e966(0x115)]>_0x367bfb[_0x58e966(0xcf)]?(_0x294ac4[_0x58e966(0x10d)]='',delete _0x294ac4[_0x58e966(0xa7)]):_0x5e11f9>_0x3d317c&&(_0x294ac4[_0x58e966(0x10d)]=_0x294ac4[_0x58e966(0xa7)]['substr'](0x0,_0x3d317c),delete _0x294ac4[_0x58e966(0xa7)]);}}[_0xd5a59d(0xed)](_0x483f52){var _0x1cd051=_0xd5a59d;return!!(_0x483f52&&_0x1c6eb0[_0x1cd051(0xd1)]&&this['_objectToString'](_0x483f52)===_0x1cd051(0x132)&&_0x483f52[_0x1cd051(0x92)]);}[_0xd5a59d(0xd6)](_0x4af33b){var _0x552947=_0xd5a59d;if(_0x4af33b[_0x552947(0xe6)](/^\\\\d+$/))return _0x4af33b;var _0x5edc47;try{_0x5edc47=JSON[_0x552947(0xd5)](''+_0x4af33b);}catch{_0x5edc47='\\\\x22'+this[_0x552947(0x147)](_0x4af33b)+'\\\\x22';}return _0x5edc47['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x5edc47=_0x5edc47[_0x552947(0x14c)](0x1,_0x5edc47[_0x552947(0xf5)]-0x2):_0x5edc47=_0x5edc47[_0x552947(0xa1)](/'/g,'\\\\x5c\\\\x27')['replace'](/\\\\\\\\\\\"/g,'\\\\x22')['replace'](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x5edc47;}['_processTreeNodeResult'](_0x24308a,_0xf8dc4c,_0x216094,_0x2d0ea1){var _0x47078e=_0xd5a59d;this[_0x47078e(0xc8)](_0x24308a,_0xf8dc4c),_0x2d0ea1&&_0x2d0ea1(),this[_0x47078e(0xbb)](_0x216094,_0x24308a),this[_0x47078e(0xda)](_0x24308a,_0xf8dc4c);}['_treeNodePropertiesBeforeFullValue'](_0x967358,_0x6a7c01){var _0x5538e8=_0xd5a59d;this[_0x5538e8(0xf7)](_0x967358,_0x6a7c01),this[_0x5538e8(0xab)](_0x967358,_0x6a7c01),this[_0x5538e8(0xa6)](_0x967358,_0x6a7c01),this[_0x5538e8(0x16f)](_0x967358,_0x6a7c01);}[_0xd5a59d(0xf7)](_0x281a69,_0x1fdaf3){}[_0xd5a59d(0xab)](_0x55e132,_0x287d38){}[_0xd5a59d(0x14a)](_0x415c7b,_0x581b77){}['_isUndefined'](_0xd3c5ae){return _0xd3c5ae===this['_undefined'];}[_0xd5a59d(0xda)](_0x100ee1,_0x1e47ad){var _0x192f28=_0xd5a59d;this['_setNodeLabel'](_0x100ee1,_0x1e47ad),this['_setNodeExpandableState'](_0x100ee1),_0x1e47ad[_0x192f28(0x174)]&&this[_0x192f28(0x16b)](_0x100ee1),this[_0x192f28(0x9f)](_0x100ee1,_0x1e47ad),this[_0x192f28(0x124)](_0x100ee1,_0x1e47ad),this['_cleanNode'](_0x100ee1);}['_additionalMetadata'](_0x13eec3,_0x31c974){var _0x5775f4=_0xd5a59d;try{_0x13eec3&&typeof _0x13eec3[_0x5775f4(0xf5)]=='number'&&(_0x31c974[_0x5775f4(0xf5)]=_0x13eec3[_0x5775f4(0xf5)]);}catch{}if(_0x31c974[_0x5775f4(0x162)]==='number'||_0x31c974['type']===_0x5775f4(0xee)){if(isNaN(_0x31c974[_0x5775f4(0xa7)]))_0x31c974[_0x5775f4(0x125)]=!0x0,delete _0x31c974[_0x5775f4(0xa7)];else switch(_0x31c974[_0x5775f4(0xa7)]){case Number[_0x5775f4(0x96)]:_0x31c974['positiveInfinity']=!0x0,delete _0x31c974['value'];break;case Number[_0x5775f4(0x123)]:_0x31c974[_0x5775f4(0x173)]=!0x0,delete _0x31c974[_0x5775f4(0xa7)];break;case 0x0:this['_isNegativeZero'](_0x31c974[_0x5775f4(0xa7)])&&(_0x31c974[_0x5775f4(0x168)]=!0x0);break;}}else _0x31c974[_0x5775f4(0x162)]===_0x5775f4(0x144)&&typeof _0x13eec3[_0x5775f4(0x17b)]==_0x5775f4(0xb7)&&_0x13eec3[_0x5775f4(0x17b)]&&_0x31c974[_0x5775f4(0x17b)]&&_0x13eec3[_0x5775f4(0x17b)]!==_0x31c974[_0x5775f4(0x17b)]&&(_0x31c974[_0x5775f4(0xbc)]=_0x13eec3[_0x5775f4(0x17b)]);}[_0xd5a59d(0x118)](_0x3f62bd){return 0x1/_0x3f62bd===Number['NEGATIVE_INFINITY'];}[_0xd5a59d(0x16b)](_0x2fe6ed){var _0x387e2c=_0xd5a59d;!_0x2fe6ed[_0x387e2c(0xde)]||!_0x2fe6ed[_0x387e2c(0xde)][_0x387e2c(0xf5)]||_0x2fe6ed['type']===_0x387e2c(0x103)||_0x2fe6ed[_0x387e2c(0x162)]===_0x387e2c(0xd1)||_0x2fe6ed[_0x387e2c(0x162)]===_0x387e2c(0x186)||_0x2fe6ed['props']['sort'](function(_0x3b9b24,_0x56671c){var _0x509fa8=_0x387e2c,_0x4f6678=_0x3b9b24[_0x509fa8(0x17b)]['toLowerCase'](),_0x390f8d=_0x56671c[_0x509fa8(0x17b)]['toLowerCase']();return _0x4f6678<_0x390f8d?-0x1:_0x4f6678>_0x390f8d?0x1:0x0;});}['_addFunctionsNode'](_0xdef430,_0x1d4f47){var _0x1a0043=_0xd5a59d;if(!(_0x1d4f47[_0x1a0043(0xb9)]||!_0xdef430[_0x1a0043(0xde)]||!_0xdef430[_0x1a0043(0xde)]['length'])){for(var _0x15afac=[],_0x109a59=[],_0x281f7a=0x0,_0x5cdfbc=_0xdef430[_0x1a0043(0xde)]['length'];_0x281f7a<_0x5cdfbc;_0x281f7a++){var _0xa80335=_0xdef430['props'][_0x281f7a];_0xa80335[_0x1a0043(0x162)]===_0x1a0043(0x144)?_0x15afac['push'](_0xa80335):_0x109a59[_0x1a0043(0xb1)](_0xa80335);}if(!(!_0x109a59[_0x1a0043(0xf5)]||_0x15afac[_0x1a0043(0xf5)]<=0x1)){_0xdef430[_0x1a0043(0xde)]=_0x109a59;var _0x20b1db={'functionsNode':!0x0,'props':_0x15afac};this[_0x1a0043(0xf7)](_0x20b1db,_0x1d4f47),this['_setNodeLabel'](_0x20b1db,_0x1d4f47),this[_0x1a0043(0x112)](_0x20b1db),this['_setNodePermissions'](_0x20b1db,_0x1d4f47),_0x20b1db['id']+='\\\\x20f',_0xdef430[_0x1a0043(0xde)][_0x1a0043(0xef)](_0x20b1db);}}}['_addLoadNode'](_0x2d4ed7,_0x10c69f){}[_0xd5a59d(0x112)](_0x1eb55a){}[_0xd5a59d(0xd0)](_0x471991){var _0x235d83=_0xd5a59d;return Array[_0x235d83(0x97)](_0x471991)||typeof _0x471991==_0x235d83(0x110)&&this[_0x235d83(0x147)](_0x471991)===_0x235d83(0xca);}[_0xd5a59d(0x16f)](_0x573363,_0x378b53){}[_0xd5a59d(0x17c)](_0x1bd6da){var _0xbe21f4=_0xd5a59d;delete _0x1bd6da[_0xbe21f4(0x178)],delete _0x1bd6da['_hasSetOnItsPath'],delete _0x1bd6da[_0xbe21f4(0x149)];}[_0xd5a59d(0xa6)](_0x35d1ef,_0x4ed53b){}}let _0x53d974=new _0x4a2137(),_0x3eb1c5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x550602={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x259d6c(_0x2e3779,_0xd917b2,_0xb327ac,_0x24707f,_0x4ad55e,_0x4f5466){var _0x4b9f72=_0xd5a59d;let _0x4ea472,_0x5b4197;try{_0x5b4197=_0x57d945(),_0x4ea472=_0x51de18[_0xd917b2],!_0x4ea472||_0x5b4197-_0x4ea472['ts']>0x1f4&&_0x4ea472[_0x4b9f72(0x11f)]&&_0x4ea472[_0x4b9f72(0x137)]/_0x4ea472[_0x4b9f72(0x11f)]<0x64?(_0x51de18[_0xd917b2]=_0x4ea472={'count':0x0,'time':0x0,'ts':_0x5b4197},_0x51de18[_0x4b9f72(0x17a)]={}):_0x5b4197-_0x51de18[_0x4b9f72(0x17a)]['ts']>0x32&&_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x11f)]&&_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x137)]/_0x51de18[_0x4b9f72(0x17a)]['count']<0x64&&(_0x51de18[_0x4b9f72(0x17a)]={});let _0x10d134=[],_0x59cb67=_0x4ea472[_0x4b9f72(0x142)]||_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x142)]?_0x550602:_0x3eb1c5,_0x594003=_0x5e3009=>{var _0x2d7700=_0x4b9f72;let _0xa0775f={};return _0xa0775f[_0x2d7700(0xde)]=_0x5e3009[_0x2d7700(0xde)],_0xa0775f[_0x2d7700(0xcd)]=_0x5e3009['elements'],_0xa0775f['strLength']=_0x5e3009['strLength'],_0xa0775f[_0x2d7700(0xcf)]=_0x5e3009['totalStrLength'],_0xa0775f[_0x2d7700(0x14e)]=_0x5e3009[_0x2d7700(0x14e)],_0xa0775f[_0x2d7700(0xe9)]=_0x5e3009[_0x2d7700(0xe9)],_0xa0775f[_0x2d7700(0x174)]=!0x1,_0xa0775f['noFunctions']=!_0x552919,_0xa0775f[_0x2d7700(0xaf)]=0x1,_0xa0775f[_0x2d7700(0xe0)]=0x0,_0xa0775f[_0x2d7700(0x91)]=_0x2d7700(0x18a),_0xa0775f[_0x2d7700(0xfb)]=_0x2d7700(0x13a),_0xa0775f[_0x2d7700(0x156)]=!0x0,_0xa0775f['autoExpandPreviousObjects']=[],_0xa0775f[_0x2d7700(0xa9)]=0x0,_0xa0775f[_0x2d7700(0xdb)]=!0x0,_0xa0775f[_0x2d7700(0x115)]=0x0,_0xa0775f[_0x2d7700(0x182)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0xa0775f;};for(var _0x4e3ef1=0x0;_0x4e3ef1<_0x4ad55e[_0x4b9f72(0xf5)];_0x4e3ef1++)_0x10d134[_0x4b9f72(0xb1)](_0x53d974[_0x4b9f72(0xe8)]({'timeNode':_0x2e3779===_0x4b9f72(0x137)||void 0x0},_0x4ad55e[_0x4e3ef1],_0x594003(_0x59cb67),{}));if(_0x2e3779===_0x4b9f72(0xad)||_0x2e3779===_0x4b9f72(0x16a)){let _0x597c0d=Error[_0x4b9f72(0x153)];try{Error['stackTraceLimit']=0x1/0x0,_0x10d134['push'](_0x53d974[_0x4b9f72(0xe8)]({'stackNode':!0x0},new Error()[_0x4b9f72(0x163)],_0x594003(_0x59cb67),{'strLength':0x1/0x0}));}finally{Error[_0x4b9f72(0x153)]=_0x597c0d;}}return{'method':_0x4b9f72(0xd7),'version':_0x533c9c,'args':[{'ts':_0xb327ac,'session':_0x24707f,'args':_0x10d134,'id':_0xd917b2,'context':_0x4f5466}]};}catch(_0x541082){return{'method':'log','version':_0x533c9c,'args':[{'ts':_0xb327ac,'session':_0x24707f,'args':[{'type':'unknown','error':_0x541082&&_0x541082['message']}],'id':_0xd917b2,'context':_0x4f5466}]};}finally{try{if(_0x4ea472&&_0x5b4197){let _0x24841e=_0x57d945();_0x4ea472['count']++,_0x4ea472[_0x4b9f72(0x137)]+=_0x1a1b80(_0x5b4197,_0x24841e),_0x4ea472['ts']=_0x24841e,_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x11f)]++,_0x51de18['hits'][_0x4b9f72(0x137)]+=_0x1a1b80(_0x5b4197,_0x24841e),_0x51de18[_0x4b9f72(0x17a)]['ts']=_0x24841e,(_0x4ea472[_0x4b9f72(0x11f)]>0x32||_0x4ea472['time']>0x64)&&(_0x4ea472[_0x4b9f72(0x142)]=!0x0),(_0x51de18['hits']['count']>0x3e8||_0x51de18['hits'][_0x4b9f72(0x137)]>0x12c)&&(_0x51de18['hits']['reduceLimits']=!0x0);}}catch{}}}return _0x259d6c;}((_0x5e47ad,_0x378d13,_0x36c5af,_0x2ad400,_0x3b6c41,_0x3d43c6,_0x2ffba0,_0xb9f3b,_0x1807ca,_0x39281c,_0xab83d7)=>{var _0x3e722f=_0x2521ed;if(_0x5e47ad[_0x3e722f(0xf8)])return _0x5e47ad[_0x3e722f(0xf8)];if(!X(_0x5e47ad,_0xb9f3b,_0x3b6c41))return _0x5e47ad['_console_ninja']={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x5e47ad[_0x3e722f(0xf8)];let _0x48b6db=B(_0x5e47ad),_0x57ac12=_0x48b6db['elapsed'],_0xe33db9=_0x48b6db[_0x3e722f(0x120)],_0x526af0=_0x48b6db[_0x3e722f(0x15d)],_0x1dd57c={'hits':{},'ts':{}},_0x3d768e=J(_0x5e47ad,_0x1807ca,_0x1dd57c,_0x3d43c6),_0x5f2366=_0x361547=>{_0x1dd57c['ts'][_0x361547]=_0xe33db9();},_0x2958f1=(_0x36dda4,_0x58c809)=>{var _0x8e36ec=_0x3e722f;let _0x3e9662=_0x1dd57c['ts'][_0x58c809];if(delete _0x1dd57c['ts'][_0x58c809],_0x3e9662){let _0xc25b2=_0x57ac12(_0x3e9662,_0xe33db9());_0x3833c3(_0x3d768e(_0x8e36ec(0x137),_0x36dda4,_0x526af0(),_0x4e4a80,[_0xc25b2],_0x58c809));}},_0x55e353=_0x3c2726=>{var _0x4c18bf=_0x3e722f,_0x470830;return _0x3b6c41==='next.js'&&_0x5e47ad[_0x4c18bf(0x11b)]&&((_0x470830=_0x3c2726==null?void 0x0:_0x3c2726[_0x4c18bf(0x157)])==null?void 0x0:_0x470830[_0x4c18bf(0xf5)])&&(_0x3c2726[_0x4c18bf(0x157)][0x0]['origin']=_0x5e47ad['origin']),_0x3c2726;};_0x5e47ad['_console_ninja']={'consoleLog':(_0xb1db27,_0x4d7fde)=>{var _0x41fac5=_0x3e722f;_0x5e47ad[_0x41fac5(0xcc)]['log']['name']!==_0x41fac5(0x119)&&_0x3833c3(_0x3d768e(_0x41fac5(0xd7),_0xb1db27,_0x526af0(),_0x4e4a80,_0x4d7fde));},'consoleTrace':(_0x3d2b3f,_0x29f758)=>{var _0x3bb354=_0x3e722f,_0x24fd68,_0x565109;_0x5e47ad[_0x3bb354(0xcc)][_0x3bb354(0xd7)]['name']!==_0x3bb354(0x18e)&&((_0x565109=(_0x24fd68=_0x5e47ad['process'])==null?void 0x0:_0x24fd68['versions'])!=null&&_0x565109[_0x3bb354(0x182)]&&(_0x5e47ad[_0x3bb354(0x169)]=!0x0),_0x3833c3(_0x55e353(_0x3d768e('trace',_0x3d2b3f,_0x526af0(),_0x4e4a80,_0x29f758))));},'consoleError':(_0x2905a8,_0x5e8589)=>{var _0x404511=_0x3e722f;_0x5e47ad[_0x404511(0x169)]=!0x0,_0x3833c3(_0x55e353(_0x3d768e(_0x404511(0x16a),_0x2905a8,_0x526af0(),_0x4e4a80,_0x5e8589)));},'consoleTime':_0x2fbfbe=>{_0x5f2366(_0x2fbfbe);},'consoleTimeEnd':(_0x259732,_0x559171)=>{_0x2958f1(_0x559171,_0x259732);},'autoLog':(_0x40568e,_0x2ce346)=>{var _0x5b92d9=_0x3e722f;_0x3833c3(_0x3d768e(_0x5b92d9(0xd7),_0x2ce346,_0x526af0(),_0x4e4a80,[_0x40568e]));},'autoLogMany':(_0x382967,_0x4639f2)=>{var _0x5698ae=_0x3e722f;_0x3833c3(_0x3d768e(_0x5698ae(0xd7),_0x382967,_0x526af0(),_0x4e4a80,_0x4639f2));},'autoTrace':(_0x85edef,_0x1971fc)=>{var _0x323975=_0x3e722f;_0x3833c3(_0x55e353(_0x3d768e(_0x323975(0xad),_0x1971fc,_0x526af0(),_0x4e4a80,[_0x85edef])));},'autoTraceMany':(_0x423cb2,_0x132601)=>{var _0x429ba5=_0x3e722f;_0x3833c3(_0x55e353(_0x3d768e(_0x429ba5(0xad),_0x423cb2,_0x526af0(),_0x4e4a80,_0x132601)));},'autoTime':(_0x4093c2,_0x187089,_0x3a3847)=>{_0x5f2366(_0x3a3847);},'autoTimeEnd':(_0xd69daa,_0x4e00cb,_0x12447b)=>{_0x2958f1(_0x4e00cb,_0x12447b);},'coverage':_0x28f0a5=>{var _0x5963e3=_0x3e722f;_0x3833c3({'method':_0x5963e3(0x13e),'version':_0x3d43c6,'args':[{'id':_0x28f0a5}]});}};let _0x3833c3=H(_0x5e47ad,_0x378d13,_0x36c5af,_0x2ad400,_0x3b6c41,_0x39281c,_0xab83d7),_0x4e4a80=_0x5e47ad['_console_ninja_session'];return _0x5e47ad[_0x3e722f(0xf8)];})(globalThis,_0x2521ed(0x10f),_0x2521ed(0xb0),_0x2521ed(0xfc),_0x2521ed(0x12c),'1.0.0','1753435696100',_0x2521ed(0xc3),_0x2521ed(0x10e),'','1');\");\n    } catch (e) {}\n}\nfunction oo_oo(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleLog(i, v);\n    } catch (e) {}\n    return v;\n}\noo_oo; /* istanbul ignore next */ \nfunction oo_tr(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleTrace(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tr; /* istanbul ignore next */ \nfunction oo_tx(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleError(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tx; /* istanbul ignore next */ \nfunction oo_ts(v) {\n    try {\n        oo_cm().consoleTime(v);\n    } catch (e) {}\n    return v;\n}\noo_ts; /* istanbul ignore next */ \nfunction oo_te(v, i) {\n    try {\n        oo_cm().consoleTimeEnd(v, i);\n    } catch (e) {}\n    return v;\n}\noo_te; /*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/ \nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"CopyButton\");\n$RefreshReg$(_c1, \"WarningIconWithPopover\");\n$RefreshReg$(_c2, \"ManifestActionButton\");\n$RefreshReg$(_c3, \"Column\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL3RyYWNrU2hlZXRzL2NvbHVtbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0U7QUFDOUM7QUFDd0I7QUFLeEI7QUFDaUI7QUFPNUI7QUFDbUI7QUFDVjtBQUNvQjtBQUNFO0FBQ087QUFHNUI7QUFDNkM7QUFDN0I7QUFPdEI7QUFFMUJDLDhEQUFjQSxDQUFDcUIsZUFBZSxDQUFDO0lBQUN0QixrRUFBa0JBO0NBQUM7QUFFbkQsc0NBQXNDO0FBQ3RDLE1BQU11QixhQUFhO1FBQUMsRUFDbEJDLElBQUksRUFDSkMsV0FBVyxLQUFLLEVBSWpCOztJQUNDLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHZCwrQ0FBUUEsQ0FBQztJQUVyQyxNQUFNZSxhQUFhLE9BQU9DO1FBQ3hCQSxFQUFFQyxlQUFlLElBQUksd0JBQXdCO1FBRTdDLElBQUlMLFlBQVksQ0FBQ0QsUUFBUUEsU0FBUywwQkFBMEI7WUFDMURWLHlDQUFLQSxDQUFDaUIsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNQyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ1Y7WUFDcENHLFVBQVU7WUFDVmIseUNBQUtBLENBQUNxQixPQUFPLENBQUM7WUFFZCx5Q0FBeUM7WUFDekNDLFdBQVcsSUFBTVQsVUFBVSxRQUFRO1FBQ3JDLEVBQUUsT0FBT1UsS0FBSztZQUNadkIseUNBQUtBLENBQUNpQixLQUFLLENBQUM7WUFDWixrQkFBa0IsR0FBRU8sUUFBUVAsS0FBSyxJQUFJUSxNQUFPLDRCQUEwQix5QkFBeUJGO1FBQ2pHO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0c7UUFDQ0MsU0FBU2I7UUFDVEgsVUFBVUE7UUFDVmlCLFdBQVcsaUZBTVIsT0FIQ2pCLFdBQ0kscUNBQ0EseUVBQ0w7UUFFSGtCLE9BQU9sQixXQUFXLHlCQUF5QjtrQkFFMUNDLHVCQUNDLDhEQUFDakIsOEhBQUtBO1lBQUNpQyxXQUFVOzs7OztzQ0FFakIsOERBQUNsQyw4SEFBSUE7WUFBQ2tDLFdBQVU7Ozs7Ozs7Ozs7O0FBSXhCO0dBbkRNbkI7S0FBQUE7QUFxRE4sZ0RBQWdEO0FBQ2hELE1BQU1xQix5QkFBeUI7UUFBQyxFQUFFQyxRQUFRLEVBQXVCO0lBQy9ELElBQUksQ0FBQ0EsWUFBWUEsU0FBU0MsTUFBTSxLQUFLLEdBQUcsT0FBTztJQUUvQyw2QkFBNkI7SUFDN0IsTUFBTUMsVUFBVUYsU0FBU0csTUFBTSxDQUFDLENBQUNDLEtBQVVDO1FBQ3pDRCxHQUFHLENBQUNDLEVBQUVDLFFBQVEsQ0FBQyxHQUFHRixHQUFHLENBQUNDLEVBQUVDLFFBQVEsQ0FBQyxJQUFJLEVBQUU7UUFDdkNGLEdBQUcsQ0FBQ0MsRUFBRUMsUUFBUSxDQUFDLENBQUNDLElBQUksQ0FBQ0Y7UUFDckIsT0FBT0Q7SUFDVCxHQUFHLENBQUM7SUFFSixxQkFDRSw4REFBQzdDLDJEQUFPQTs7MEJBQ04sOERBQUNDLGtFQUFjQTtnQkFBQ2dELE9BQU87MEJBQ3JCLDRFQUFDQztvQkFBS1osV0FBVTs4QkFDZCw0RUFBQ2hDLDhIQUFZQTt3QkFBQ2dDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBRzVCLDhEQUFDcEMsa0VBQWNBO2dCQUFDb0MsV0FBVTs7a0NBQ3hCLDhEQUFDYTt3QkFBSWIsV0FBVTtrQ0FBMEM7Ozs7OztvQkFHeEQ7d0JBQUM7d0JBQVk7d0JBQVE7cUJBQVMsQ0FBQ2MsR0FBRyxDQUNqQyxDQUFDQyxNQUNDVixPQUFPLENBQUNVLElBQUksSUFDWlYsT0FBTyxDQUFDVSxJQUFJLENBQUNYLE1BQU0sR0FBRyxtQkFDcEIsOERBQUNTOzRCQUFjYixXQUFVOzs4Q0FDdkIsOERBQUNhO29DQUNDYixXQUNFZSxRQUFRLGFBQ0osbURBQ0FBLFFBQVEsU0FDUixtREFDQTs7d0NBR0xBLFFBQVEsNEJBQWMsOERBQUM3Qyw4SEFBV0E7NENBQUM4QixXQUFVOzs7Ozs7d0NBQzdDZSxRQUFRLHdCQUFVLDhEQUFDL0MsOEhBQVlBOzRDQUFDZ0MsV0FBVTs7Ozs7O3dDQUMxQ2UsUUFBUSwwQkFBWSw4REFBQzlDLDhIQUFhQTs0Q0FBQytCLFdBQVU7Ozs7Ozt3Q0FDN0NlOzs7Ozs7OzhDQUVILDhEQUFDQztvQ0FBR2hCLFdBQVU7OENBQ1hLLE9BQU8sQ0FBQ1UsSUFBSSxDQUFDRCxHQUFHLENBQUMsQ0FBQ04sR0FBUVMsa0JBQ3pCLDhEQUFDQztzREFBWVYsRUFBRVcsT0FBTzsyQ0FBYkY7Ozs7Ozs7Ozs7OzJCQWpCTEY7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBMEJ4QjtNQW5ETWI7QUFxRE5BLHVCQUF1QmtCLFdBQVcsR0FBRztBQUVyQyxxRUFBcUU7QUFDckUsTUFBTUMsdUJBQXVCO1FBQUMsRUFDNUJDLFVBQVUsRUFDVkMsUUFBUSxFQUlUOztJQUNDLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUc5QyxxREFBYyxDQUFDO0lBQ3ZELE1BQU0rQyxvQkFBb0IvQyxtREFBWSxDQUFpQztJQUV2RSxNQUFNaUQsbUJBQW1CO1FBQ3ZCSCxnQkFBZ0I7UUFDaEIvQixXQUFXO2dCQUNUZ0M7YUFBQUEsNkJBQUFBLGtCQUFrQkcsT0FBTyxjQUF6QkgsaURBQUFBLDJCQUEyQkksb0JBQW9CLENBQUNSLHVCQUFBQSxpQ0FBQUEsV0FBWVMsRUFBRTtRQUNoRSxHQUFHO0lBQ0w7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUNyRCwwREFBTUE7Z0JBQ0xzRCxTQUFRO2dCQUNSaEMsV0FBVTtnQkFDVkQsU0FBUzZCOzBCQUVULDRFQUFDbkQsdUdBQWlCQTs7Ozs7Ozs7OztZQUVuQitDLDhCQUNDLDhEQUFDaEQsOERBQW9CQTtnQkFDbkJ5RCxLQUFLUDtnQkFDTFEsWUFBWSxFQUFFWix1QkFBQUEsaUNBQUFBLFdBQVlTLEVBQUU7Z0JBQzVCUCxjQUFjQTtnQkFDZEMsaUJBQWlCQTtnQkFDakJGLFVBQVVBOzs7Ozs7OztBQUtwQjtJQXJDTUY7TUFBQUE7QUFzQ05BLHFCQUFxQkQsV0FBVyxHQUFHO0FBRW5DLE1BQU1lLFNBQVMsQ0FDYkMsYUFDQUMsZ0JBQ0FDLFlBQ0FDLG1CQUNBQyxrQkFDQWpCO1FBQ0EsRUFDRWtCLGVBQWUsRUFDZkMsZUFBZSxFQUNmQyxrQkFBa0IsRUFLbkI7SUFFRCxNQUFNQyxjQUF3QjtRQUM1QjtZQUNFQyxPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFBV0EscUJBQUFBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG9DQUFBQSxzQkFBQUEsYUFBYUUsTUFBTSxjQUFuQkYsMENBQUFBLG9CQUFxQkcsV0FBVyxLQUFJOztZQUM3REMsUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYVMsT0FBTyxLQUFJOztZQUNqREwsUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYVUsUUFBUSxLQUFJOztZQUNsRE4sUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQSxzQkFBQUE7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsb0NBQUFBLHVCQUFBQSxhQUFhVyxPQUFPLGNBQXBCWCwyQ0FBQUEscUJBQXNCWSxJQUFJLEtBQUk7O1lBQ3ZEUixRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQVdBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhYSxXQUFXLEtBQUk7O1lBQ3JEVCxRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQVdBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhYyxPQUFPLEtBQUk7O1lBQ2pEVixRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1ppQixjQUFjLENBQUNmO29CQUNJQTtnQkFBakIsTUFBTWdCLFlBQVdoQixlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhZ0IsUUFBUTtnQkFDdEMsTUFBTUMsY0FBY0QsWUFBWUEsYUFBYTtnQkFDN0MsTUFBTUUsY0FBY0QsY0FBY0QsV0FBVztnQkFFN0MscUJBQ0UsOERBQUNuRDtvQkFBSWIsV0FBVTs7c0NBQ2IsOERBQUNuQjs0QkFDQ0MsTUFBTW1GLGNBQWNELFdBQVc7NEJBQy9CakYsVUFBVSxDQUFDa0Y7Ozs7OztzQ0FFYiw4REFBQ3JEOzRCQUNDWixXQUNFLGtDQUNDaUUsQ0FBQUEsY0FDRyxpQ0FDQSw4QkFBNkI7NEJBRW5DaEUsT0FBT2lFOzRCQUNQQyxPQUFPO2dDQUFFQyxVQUFVOzRCQUFFO3NDQUVwQkY7Ozs7OztzQ0FFSCw4REFBQ3JEOzRCQUFJYixXQUFVOzs7Ozs7Ozs7Ozs7WUFHckI7WUFDQStDLGFBQWEsQ0FBQ0M7b0JBQ0tBO2dCQUFqQixNQUFNZ0IsWUFBV2hCLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWFnQixRQUFRO2dCQUN0QyxJQUFJLENBQUNBLFlBQVlBLGFBQWEsT0FBTztvQkFDbkMsT0FBTztnQkFDVDtnQkFDQSxPQUFPQTtZQUNUO1lBQ0FaLFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BjLFdBQVcsSUFBTztvQkFDaEJDLFNBQVM7b0JBQ1RDLFlBQVk7b0JBQ1pDLFNBQVM7b0JBQ1RDLFFBQVE7b0JBQ1JDLGFBQWE7b0JBQ2JDLFlBQVk7b0JBQ1pDLFVBQVU7b0JBQ1ZDLGNBQWM7Z0JBQ2hCO1lBQ0FDLG9CQUFvQixDQUFDOUI7b0JBQ0ZBO2dCQUFqQixNQUFNZ0IsWUFBV2hCLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWFnQixRQUFRO2dCQUN0QyxJQUFJLENBQUNBLFlBQVlBLGFBQWEsT0FBTztvQkFDbkMsT0FBTztnQkFDVDtnQkFDQSxPQUFPQTtZQUNUO1lBQ0FSLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFBV0E7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWErQixhQUFhLEtBQUk7O1lBQ3ZEM0IsUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYWdDLE9BQU8sS0FBSTs7WUFDakQ1QixRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQVdBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhaUMsR0FBRyxLQUFJOztZQUM3QzdCLFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWm9DLGdCQUFnQixDQUFDbEM7Z0JBQ2YsSUFBSSxDQUFDQSxPQUFPbUMsS0FBSyxFQUFFLE9BQU87Z0JBQzFCLE1BQU1DLEtBQUs1SCwyQ0FBUUEsQ0FBQzZILE9BQU8sQ0FBQ3JDLE9BQU9tQyxLQUFLLEVBQUU7b0JBQUVHLE1BQU07Z0JBQU07Z0JBQ3hELE9BQU9GLEdBQUdHLE9BQU8sR0FBR0gsR0FBR0ksUUFBUSxDQUFDLGdCQUFnQjtZQUNsRDtZQUNBcEMsUUFBUTtZQUNSQyxjQUFjO2dCQUNab0MsWUFBWSxDQUFDQywyQkFBaUNDO29CQUM1QyxJQUFJLENBQUNBLFdBQVcsT0FBTyxDQUFDO29CQUN4QixNQUFNQyxXQUFXcEksMkNBQVFBLENBQUM2SCxPQUFPLENBQUNNLFdBQVc7d0JBQUVMLE1BQU07b0JBQU0sR0FDeERPLE9BQU8sQ0FBQyxPQUNSQyxRQUFRO29CQUNYLE1BQU1DLGFBQWEsSUFBSUMsS0FDckJBLEtBQUtDLEdBQUcsQ0FDTlAsMEJBQTBCUSxXQUFXLElBQ3JDUiwwQkFBMEJTLFFBQVEsSUFDbENULDBCQUEwQlUsT0FBTztvQkFHckMsSUFBSVIsV0FBV0csWUFBWSxPQUFPLENBQUM7b0JBQ25DLElBQUlILFdBQVdHLFlBQVksT0FBTztvQkFDbEMsT0FBTztnQkFDVDtnQkFDQXpDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQ0NBO2dCQUFiLE1BQU1xRCxRQUFPckQsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYXNELFdBQVc7Z0JBQ3JDLE9BQU9ELE9BQ0g3SSwyQ0FBUUEsQ0FBQzZILE9BQU8sQ0FBQ2dCLE1BQU07b0JBQUVmLE1BQU07Z0JBQU0sR0FBR0UsUUFBUSxDQUFDLGdCQUNqRDtZQUNOO1lBQ0FwQyxRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pvQyxZQUFZLENBQUNDLDJCQUEyQkM7b0JBQ3RDLElBQUksQ0FBQ0EsV0FBVyxPQUFPLENBQUM7b0JBQ3hCLE1BQU1DLFdBQVdwSSwyQ0FBUUEsQ0FBQzZILE9BQU8sQ0FBQ00sV0FBVzt3QkFBRUwsTUFBTTtvQkFBTSxHQUN4RE8sT0FBTyxDQUFDLE9BQ1JDLFFBQVE7b0JBQ1gsTUFBTUMsYUFBYSxJQUFJQyxLQUNyQkEsS0FBS0MsR0FBRyxDQUNOUCwwQkFBMEJRLFdBQVcsSUFDckNSLDBCQUEwQlMsUUFBUSxJQUNsQ1QsMEJBQTBCVSxPQUFPO29CQUdyQyxJQUFJUixXQUFXRyxZQUFZLE9BQU8sQ0FBQztvQkFDbkMsSUFBSUgsV0FBV0csWUFBWSxPQUFPO29CQUNsQyxPQUFPO2dCQUNUO2dCQUNBekMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFDQ0E7Z0JBQWIsTUFBTXFELFFBQU9yRCxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhdUQsWUFBWTtnQkFDdEMsT0FBT0YsT0FDSDdJLDJDQUFRQSxDQUFDNkgsT0FBTyxDQUFDZ0IsTUFBTTtvQkFBRWYsTUFBTTtnQkFBTSxHQUFHRSxRQUFRLENBQUMsZ0JBQ2pEO1lBQ047WUFDQXBDLFFBQVE7WUFDUkMsY0FBYztnQkFDWm9DLFlBQVksQ0FBQ0MsMkJBQTJCQztvQkFDdEMsSUFBSSxDQUFDQSxXQUFXLE9BQU8sQ0FBQztvQkFDeEIsTUFBTUMsV0FBV3BJLDJDQUFRQSxDQUFDNkgsT0FBTyxDQUFDTSxXQUFXO3dCQUFFTCxNQUFNO29CQUFNLEdBQ3hETyxPQUFPLENBQUMsT0FDUkMsUUFBUTtvQkFDWCxNQUFNQyxhQUFhLElBQUlDLEtBQ3JCQSxLQUFLQyxHQUFHLENBQ05QLDBCQUEwQlEsV0FBVyxJQUNyQ1IsMEJBQTBCUyxRQUFRLElBQ2xDVCwwQkFBMEJVLE9BQU87b0JBR3JDLElBQUlSLFdBQVdHLFlBQVksT0FBTyxDQUFDO29CQUNuQyxJQUFJSCxXQUFXRyxZQUFZLE9BQU87b0JBQ2xDLE9BQU87Z0JBQ1Q7Z0JBQ0F6QyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYXdELFlBQVksS0FBSTs7WUFDdERwRCxRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQVdBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFheUQsUUFBUSxLQUFJOztZQUNsRHJELFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFBV0E7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWEwRCxVQUFVLEtBQUk7O1lBQ3BEdEQsUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBRUE7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYTJELGtCQUFrQixLQUFJOztZQUM1RHZELFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFBV0E7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWE0RCxhQUFhLEtBQUk7O1lBQ3ZEeEQsUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYTZELGNBQWMsS0FBSTs7WUFDeER6RCxRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQVdBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhOEQsV0FBVyxLQUFJOztZQUNyRDFELFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFBV0E7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWErRCxjQUFjLEtBQUk7O1lBQ3hEM0QsUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYWdFLE9BQU8sS0FBSTs7WUFDakQ1RCxRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQVdBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhaUUsWUFBWSxLQUFJOztZQUN0RDdELFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFDU0E7Z0JBQXJCLE1BQU1rRSxnQkFBZWxFLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWFrRSxZQUFZO2dCQUM5QyxJQUFJQSxpQkFBaUIsTUFBTSxPQUFPO2dCQUNsQyxJQUFJQSxpQkFBaUIsT0FBTyxPQUFPO2dCQUNuQyxPQUFPO1lBQ1Q7WUFDQTlELFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtRQUNBO1lBQ0VvRixPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFBV0E7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWFtRSxZQUFZLEtBQUk7O1lBQ3REL0QsUUFBUTtZQUNSQyxjQUFjO2dCQUNaQyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO1FBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUFXQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxtQ0FBQUEsYUFBYW9FLEtBQUssS0FBSTs7WUFDL0NoRSxRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7WUFDQUMsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0I7UUFDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQVdBO3VCQUFBQSxFQUFBQSxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG1DQUFBQSxhQUFhcUUsU0FBUyxLQUFJOztZQUNuRGpFLFFBQVE7WUFDUkMsY0FBYztnQkFDWkMsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBQyxPQUFPO1lBQ1BDLGlCQUFpQi9GLG1FQUFZQTtRQUMvQjtLQUNEO0lBRUQsSUFBSWdGLG1CQUFtQjZFLE9BQU9DLElBQUksQ0FBQzlFLGlCQUFpQnJDLE1BQU0sR0FBRyxHQUFHO1FBQzlEa0gsT0FBT0MsSUFBSSxDQUFDOUUsaUJBQWlCK0UsT0FBTyxDQUFDLENBQUNDO1lBQ3BDLE1BQU1DLFlBQVlqRixlQUFlLENBQUNnRixRQUFRO1lBQzFDLE1BQU1FLFlBQVlELENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVzlELElBQUksS0FBSSxnQkFBd0IsT0FBUjZEO1lBQ3JELE1BQU1HLFlBQVlGLHNCQUFBQSxnQ0FBQUEsVUFBV0csSUFBSTtZQUNqQ2pGLFlBQVlsQyxJQUFJLENBQUM7Z0JBQ2ZtQyxPQUFPLGVBQXVCLE9BQVI0RTtnQkFDdEIzRSxZQUFZNkU7Z0JBQ1o1RSxhQUFhLENBQUNDO3dCQUNFQSwyQkFBQUE7b0JBQWQsTUFBTW1DLFNBQVFuQyxlQUFBQSxPQUFPQyxJQUFJLGNBQVhELG9DQUFBQSw0QkFBQUEsYUFBYThFLFlBQVksY0FBekI5RSxnREFBQUEseUJBQTJCLENBQUN5RSxRQUFRO29CQUNsRCxJQUFJLENBQUN0QyxPQUFPLE9BQU87b0JBQ25CLElBQUl5QyxjQUFjLFFBQVE7d0JBQ3hCLE1BQU14QyxLQUFLNUgsMkNBQVFBLENBQUM2SCxPQUFPLENBQUNGLE9BQU87NEJBQUVHLE1BQU07d0JBQU07d0JBQ2pELE9BQU9GLEdBQUdHLE9BQU8sR0FBR0gsR0FBR0ksUUFBUSxDQUFDLGdCQUFnQkw7b0JBQ2xEO29CQUNBLE9BQU9BO2dCQUNUO2dCQUNBL0IsUUFBUTtnQkFDUkMsY0FBYztvQkFBRUMsU0FBUzt3QkFBQztxQkFBUTtnQkFBQztnQkFDbkNDLE9BQU87Z0JBQ1BDLGlCQUFpQi9GLG1FQUFZQTtZQUMvQjtRQUNGO0lBQ0Y7SUFFQSwwQ0FBMEM7SUFDMUMsSUFBSWlGLGlCQUFpQjtRQUNuQkUsWUFBWWxDLElBQUksQ0FDZDtZQUNFbUMsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQ1pBLDhCQUFBQTt1QkFBQUEsRUFBQUEsZUFBQUEsT0FBT0MsSUFBSSxjQUFYRCxvQ0FBQUEsK0JBQUFBLGFBQWErRSxlQUFlLGNBQTVCL0UsbURBQUFBLDZCQUE4QmdGLGNBQWMsS0FBSTs7WUFDbEQ1RSxRQUFRO1lBQ1JDLGNBQWM7Z0JBQUVDLFNBQVM7b0JBQUM7aUJBQVE7WUFBQztZQUNuQ0MsT0FBTztZQUNQQyxpQkFBaUIvRixtRUFBWUE7UUFDL0IsR0FDQTtZQUNFb0YsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLGFBQWEsQ0FBQ0M7b0JBQ0NBLDhCQUFBQTtnQkFBYixNQUFNcUQsUUFBT3JELGVBQUFBLE9BQU9DLElBQUksY0FBWEQsb0NBQUFBLCtCQUFBQSxhQUFhK0UsZUFBZSxjQUE1Qi9FLG1EQUFBQSw2QkFBOEJpRixZQUFZO2dCQUN2RCxPQUFPNUIsT0FDSDdJLDJDQUFRQSxDQUFDNkgsT0FBTyxDQUFDZ0IsTUFBTTtvQkFBRWYsTUFBTTtnQkFBTSxHQUFHRSxRQUFRLENBQUMsZ0JBQ2pEO1lBQ047WUFDQXBDLFFBQVE7WUFDUkMsY0FBYztnQkFDWm9DLFlBQVksQ0FBQ0MsMkJBQTJCQztvQkFDdEMsSUFBSSxDQUFDQSxXQUFXLE9BQU8sQ0FBQztvQkFDeEIsTUFBTUMsV0FBV3BJLDJDQUFRQSxDQUFDNkgsT0FBTyxDQUFDTSxXQUFXO3dCQUFFTCxNQUFNO29CQUFNLEdBQ3hETyxPQUFPLENBQUMsT0FDUkMsUUFBUTtvQkFDWCxNQUFNQyxhQUFhLElBQUlDLEtBQ3JCQSxLQUFLQyxHQUFHLENBQ05QLDBCQUEwQlEsV0FBVyxJQUNyQ1IsMEJBQTBCUyxRQUFRLElBQ2xDVCwwQkFBMEJVLE9BQU87b0JBR3JDLElBQUlSLFdBQVdHLFlBQVksT0FBTyxDQUFDO29CQUNuQyxJQUFJSCxXQUFXRyxZQUFZLE9BQU87b0JBQ2xDLE9BQU87Z0JBQ1Q7Z0JBQ0F6QyxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1lBQ0FDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CLEdBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUNaQSw4QkFBQUE7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsb0NBQUFBLCtCQUFBQSxhQUFhK0UsZUFBZSxjQUE1Qi9FLG1EQUFBQSw2QkFBOEJrRixjQUFjLEtBQUk7O1lBQ2xEOUUsUUFBUTtZQUNSQyxjQUFjO2dCQUFFQyxTQUFTO29CQUFDO2lCQUFRO1lBQUM7WUFDbkNDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CLEdBQ0E7WUFDRW9GLE9BQU87WUFDUEMsWUFBWTtZQUNaQyxhQUFhLENBQUNDO29CQUNaQSw4QkFBQUE7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsb0NBQUFBLCtCQUFBQSxhQUFhK0UsZUFBZSxjQUE1Qi9FLG1EQUFBQSw2QkFBOEJtRixhQUFhLEtBQUk7O1lBQ2pEL0UsUUFBUTtZQUNSQyxjQUFjO2dCQUFFQyxTQUFTO29CQUFDO2lCQUFRO1lBQUM7WUFDbkNDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO0lBRUo7SUFDQSxJQUFJa0Ysb0JBQW9CO1FBQ3RCQyxZQUFZbEMsSUFBSSxDQUFDO1lBQ2ZtQyxPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsYUFBYSxDQUFDQztvQkFBV0E7dUJBQUFBLEVBQUFBLGVBQUFBLE9BQU9DLElBQUksY0FBWEQsbUNBQUFBLGFBQWFvRixXQUFXLEtBQUk7O1lBQ3JEaEYsUUFBUTtZQUNSQyxjQUFjO2dCQUFFQyxTQUFTO29CQUFDO2lCQUFRO1lBQUM7WUFDbkNDLE9BQU87WUFDUEMsaUJBQWlCL0YsbUVBQVlBO1FBQy9CO0lBQ0Y7SUFFQSx3Q0FBd0M7SUFDeENtRixZQUFZbEMsSUFBSSxDQUFDO1FBQ2ZtQyxPQUFPO1FBQ1BDLFlBQVk7UUFDWmlCLGNBQWMsQ0FBQ2Y7Z0JBQ0lBO1lBQWpCLE1BQU03QyxXQUFXNkMsQ0FBQUEsbUJBQUFBLDhCQUFBQSxlQUFBQSxPQUFRQyxJQUFJLGNBQVpELG1DQUFBQSxhQUFjcUYsdUJBQXVCLEtBQUksRUFBRTtZQUM1RCxxQkFBTyw4REFBQ25JO2dCQUF1QkMsVUFBVUE7Ozs7OztRQUMzQztRQUNBb0QsT0FBTztRQUNQK0UsVUFBVTtRQUNWakUsV0FBVztZQUFFQyxTQUFTO1lBQVFpRSxnQkFBZ0I7WUFBVWhFLFlBQVk7UUFBUztJQUMvRTtJQUVBM0IsWUFBWWxDLElBQUksQ0FBQztRQUNmbUMsT0FBTztRQUNQQyxZQUFZO1FBQ1ppQixjQUFjLENBQUNmO1lBQ2IsTUFBTTFCLGFBQWEwQixtQkFBQUEsNkJBQUFBLE9BQVFDLElBQUk7WUFDL0IsTUFBTTlDLFdBQVdtQixDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVkrRyx1QkFBdUIsS0FBSSxFQUFFO1lBRTFELHFCQUNFLDhEQUFDeEg7Z0JBQUliLFdBQVU7O29CQUVaMEMsaUNBQ0MsOERBQUNyQjt3QkFBcUJDLFlBQVlBO3dCQUFZQyxVQUFVQTs7Ozs7O2tDQUUxRCw4REFBQzFELHlEQUFnQkE7d0JBQ2YySyxZQUFZbEg7d0JBQ1prQixrQkFBa0JBO3dCQUNsQkQsbUJBQW1CQTs7Ozs7O2tDQUVyQiw4REFBQ2hFLHFFQUFpQkE7d0JBQ2hCNkQsYUFBYUE7d0JBQ2JxRyxxQkFBcUI7NEJBQUM7eUJBQW9CO2tDQUUxQyw0RUFBQ3BLLGdFQUFTQTs0QkFDUnFLLE9BQU8sVUFBR3BLLDhEQUFrQkEsQ0FBQ3FLLG1CQUFtQixFQUFDLEtBQWtCLE9BQWZySCx1QkFBQUEsaUNBQUFBLFdBQVlTLEVBQUU7NEJBQ2xFNkcsV0FBVyxJQUFNdkcsZUFBZSxDQUFDQzs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFLM0M7UUFDQWdHLFVBQVU7UUFDVi9FLE9BQU87UUFDUHNGLFFBQVE7UUFDUnhFLFdBQVcsSUFBTztnQkFDaEJ5RSxZQUFZO2dCQUNaakUsY0FBYztnQkFDZGtFLE9BQU87Z0JBQ1BDLFdBQVc7WUFDYjtJQUNGO0lBRUEsT0FBT3BHO0FBQ1Q7TUFsbEJNVDtBQW9sQk4sK0RBQWVBLE1BQU1BLEVBQUMsQ0FDeUIsa0JBQWtCO0FBQUcsU0FBUzhHO0lBQVEsSUFBRztRQUFDLE9BQU8sQ0FBQyxHQUFFQyxJQUFHLEVBQUcsZ0NBQWdDLENBQUMsR0FBRUEsSUFBRyxFQUFHO0lBQTRrdUMsRUFBQyxPQUFNL0osR0FBRSxDQUFDO0FBQUM7QUFBNEIsU0FBU2dLLE1BQU1sSSxDQUFRO0lBQUM7UUFBR21JLEVBQUgsMkJBQVU7O0lBQUUsSUFBRztRQUFDSCxRQUFRSSxVQUFVLENBQUNwSSxHQUFHbUk7SUFBRyxFQUFDLE9BQU1qSyxHQUFFLENBQUM7SUFBRSxPQUFPaUs7QUFBQztBQUFFRCxPQUFNLHdCQUF3QjtBQUFFLFNBQVNHLE1BQU1ySSxDQUFRO0lBQUM7UUFBR21JLEVBQUgsMkJBQVU7O0lBQUUsSUFBRztRQUFDSCxRQUFRTSxZQUFZLENBQUN0SSxHQUFHbUk7SUFBRyxFQUFDLE9BQU1qSyxHQUFFLENBQUM7SUFBRSxPQUFPaUs7QUFBQztBQUFFRSxPQUFNLHdCQUF3QjtBQUFFLFNBQVN6SixNQUFNb0IsQ0FBUTtJQUFDO1FBQUdtSSxFQUFILDJCQUFVOztJQUFFLElBQUc7UUFBQ0gsUUFBUU8sWUFBWSxDQUFDdkksR0FBR21JO0lBQUcsRUFBQyxPQUFNakssR0FBRSxDQUFDO0lBQUUsT0FBT2lLO0FBQUM7QUFBRXZKLE9BQU0sd0JBQXdCO0FBQUUsU0FBUzRKLE1BQU1MLENBQVM7SUFBUyxJQUFHO1FBQUNILFFBQVFTLFdBQVcsQ0FBQ047SUFBRyxFQUFDLE9BQU1qSyxHQUFFLENBQUM7SUFBRSxPQUFPaUs7QUFBWTtBQUFFSyxPQUFNLHdCQUF3QjtBQUFFLFNBQVNFLE1BQU1QLENBQWtCLEVBQUVuSSxDQUFRO0lBQVMsSUFBRztRQUFDZ0ksUUFBUVcsY0FBYyxDQUFDUixHQUFHbkk7SUFBRyxFQUFDLE9BQU05QixHQUFFLENBQUM7SUFBRSxPQUFPaUs7QUFBWTtBQUFFTyxPQUFNLHlRQUF5USIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvdXNlci90cmFja1NoZWV0cy9jb2x1bW4udHN4P2M5MzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQWxsQ29tbXVuaXR5TW9kdWxlLCBNb2R1bGVSZWdpc3RyeSwgQ29sRGVmIH0gZnJvbSBcImFnLWdyaWQtY29tbXVuaXR5XCI7XHJcbmltcG9ydCB7IERhdGVUaW1lIH0gZnJvbSBcImx1eG9uXCI7XHJcbmltcG9ydCBQaW5uZWRIZWFkZXIgZnJvbSBcIkAvYXBwL19jb21wb25lbnQvUGlubmVkSGVhZGVyXCI7XHJcbmltcG9ydCB7XHJcbiAgUG9wb3ZlcixcclxuICBQb3BvdmVyVHJpZ2dlcixcclxuICBQb3BvdmVyQ29udGVudCxcclxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3BvcG92ZXJcIjtcclxuaW1wb3J0IFVwZGF0ZVRyYWNrU2hlZXQgZnJvbSBcIi4vVXBkYXRlVHJhY2tTaGVldFwiO1xyXG5pbXBvcnQge1xyXG4gIENvcHksXHJcbiAgQ2hlY2ssXHJcbiAgQWxlcnRPY3RhZ29uLFxyXG4gIEFsZXJ0VHJpYW5nbGUsXHJcbiAgQWxlcnRDaXJjbGUsXHJcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSBcInNvbm5lclwiO1xyXG5pbXBvcnQgRGVsZXRlUm93IGZyb20gXCJAL2FwcC9fY29tcG9uZW50L0RlbGV0ZVJvd1wiO1xyXG5pbXBvcnQgeyB0cmFja1NoZWV0c19yb3V0ZXMgfSBmcm9tIFwiQC9saWIvcm91dGVQYXRoXCI7XHJcbmltcG9ydCB7IFBlcm1pc3Npb25XcmFwcGVyIH0gZnJvbSBcIkAvbGliL3Blcm1pc3Npb25XcmFwcGVyXCI7XHJcbmltcG9ydCBDcmVhdGVNYW5pZmVzdERldGFpbCwge1xyXG4gIENyZWF0ZU1hbmlmZXN0RGV0YWlsUmVmLFxyXG59IGZyb20gXCIuL0NyZWF0ZU1hbmlmZXN0RGV0YWlsXCI7XHJcbmltcG9ydCB7IE1kQ3JlYXRlTmV3Rm9sZGVyLCBNZE91dGxpbmVDcmVhdGVOZXdGb2xkZXIgfSBmcm9tIFwicmVhY3QtaWNvbnMvbWRcIjtcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHtcclxuICBUb29sdGlwLFxyXG4gIFRvb2x0aXBDb250ZW50LFxyXG4gIFRvb2x0aXBQcm92aWRlcixcclxuICBUb29sdGlwVHJpZ2dlcixcclxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3Rvb2x0aXBcIjtcclxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuTW9kdWxlUmVnaXN0cnkucmVnaXN0ZXJNb2R1bGVzKFtBbGxDb21tdW5pdHlNb2R1bGVdKTtcclxuXHJcbi8vIENvcHkgQnV0dG9uIENvbXBvbmVudCBmb3IgRmlsZSBQYXRoXHJcbmNvbnN0IENvcHlCdXR0b24gPSAoe1xyXG4gIHRleHQsXHJcbiAgZGlzYWJsZWQgPSBmYWxzZSxcclxufToge1xyXG4gIHRleHQ6IHN0cmluZztcclxuICBkaXNhYmxlZD86IGJvb2xlYW47XHJcbn0pID0+IHtcclxuICBjb25zdCBbY29waWVkLCBzZXRDb3BpZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICBjb25zdCBoYW5kbGVDb3B5ID0gYXN5bmMgKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcclxuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7IC8vIFByZXZlbnQgcm93IHNlbGVjdGlvblxyXG5cclxuICAgIGlmIChkaXNhYmxlZCB8fCAhdGV4dCB8fCB0ZXh0ID09PSBcIk5vIGZpbGUgcGF0aCBnZW5lcmF0ZWRcIikge1xyXG4gICAgICB0b2FzdC5lcnJvcihcIk5vIGZpbGUgcGF0aCB0byBjb3B5XCIpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodGV4dCk7XHJcbiAgICAgIHNldENvcGllZCh0cnVlKTtcclxuICAgICAgdG9hc3Quc3VjY2VzcyhcIkZpbGUgcGF0aCBjb3BpZWQgdG8gY2xpcGJvYXJkIVwiKTtcclxuXHJcbiAgICAgIC8vIFJlc2V0IHRoZSBjb3BpZWQgc3RhdGUgYWZ0ZXIgMiBzZWNvbmRzXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gc2V0Q29waWVkKGZhbHNlKSwgMjAwMCk7XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gY29weSBmaWxlIHBhdGhcIik7XHJcbiAgICAgIC8qIGVzbGludC1kaXNhYmxlICovY29uc29sZS5lcnJvciguLi5vb190eChgMjAxMjY0ODQ1NF82NF82XzY0XzQ5XzExYCxcIkZhaWxlZCB0byBjb3B5IHRleHQ6IFwiLCBlcnIpKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGJ1dHRvblxyXG4gICAgICBvbkNsaWNrPXtoYW5kbGVDb3B5fVxyXG4gICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XHJcbiAgICAgIGNsYXNzTmFtZT17YFxyXG4gICAgICAgIG1sLTIgcC0xIHJvdW5kZWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXgtc2hyaW5rLTBcclxuICAgICAgICAke1xyXG4gICAgICAgICAgZGlzYWJsZWRcclxuICAgICAgICAgICAgPyBcInRleHQtZ3JheS00MDAgY3Vyc29yLW5vdC1hbGxvd2VkXCJcclxuICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTUwIGFjdGl2ZTpiZy1ibHVlLTEwMFwiXHJcbiAgICAgICAgfVxyXG4gICAgICBgfVxyXG4gICAgICB0aXRsZT17ZGlzYWJsZWQgPyBcIk5vIGZpbGUgcGF0aCB0byBjb3B5XCIgOiBcIkNvcHkgZmlsZSBwYXRoXCJ9XHJcbiAgICA+XHJcbiAgICAgIHtjb3BpZWQgPyAoXHJcbiAgICAgICAgPENoZWNrIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmVlbi02MDBcIiAvPlxyXG4gICAgICApIDogKFxyXG4gICAgICAgIDxDb3B5IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICApfVxyXG4gICAgPC9idXR0b24+XHJcbiAgKTtcclxufTtcclxuXHJcbi8vIC0tLSBOZXc6IFdhcm5pbmdJY29uV2l0aFRvb2x0aXAgY29tcG9uZW50IC0tLVxyXG5jb25zdCBXYXJuaW5nSWNvbldpdGhQb3BvdmVyID0gKHsgd2FybmluZ3MgfTogeyB3YXJuaW5nczogYW55W10gfSkgPT4ge1xyXG4gIGlmICghd2FybmluZ3MgfHwgd2FybmluZ3MubGVuZ3RoID09PSAwKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgLy8gR3JvdXAgd2FybmluZ3MgYnkgc2V2ZXJpdHlcclxuICBjb25zdCBncm91cGVkID0gd2FybmluZ3MucmVkdWNlKChhY2M6IGFueSwgdzogYW55KSA9PiB7XHJcbiAgICBhY2Nbdy5zZXZlcml0eV0gPSBhY2Nbdy5zZXZlcml0eV0gfHwgW107XHJcbiAgICBhY2Nbdy5zZXZlcml0eV0ucHVzaCh3KTtcclxuICAgIHJldHVybiBhY2M7XHJcbiAgfSwge30pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFBvcG92ZXI+XHJcbiAgICAgIDxQb3BvdmVyVHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIHctZnVsbCBoLWZ1bGxcIj5cclxuICAgICAgICAgIDxBbGVydE9jdGFnb24gY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXJlZC02MDBcIiAvPlxyXG4gICAgICAgIDwvc3Bhbj5cclxuICAgICAgPC9Qb3BvdmVyVHJpZ2dlcj5cclxuICAgICAgPFBvcG92ZXJDb250ZW50IGNsYXNzTmFtZT1cIm1heC13LXhzIHAtM1wiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIG1iLTEgdGV4dC1yZWQtNzAwXCI+XHJcbiAgICAgICAgICBXYXJuaW5nczpcclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICB7W1wiQ1JJVElDQUxcIiwgXCJISUdIXCIsIFwiTUVESVVNXCJdLm1hcChcclxuICAgICAgICAgIChzZXYpID0+XHJcbiAgICAgICAgICAgIGdyb3VwZWRbc2V2XSAmJlxyXG4gICAgICAgICAgICBncm91cGVkW3Nldl0ubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e3Nldn0gY2xhc3NOYW1lPVwibWItMVwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xyXG4gICAgICAgICAgICAgICAgICAgIHNldiA9PT0gXCJDUklUSUNBTFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgdGV4dC1yZWQtOTAwIGZvbnQtYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA6IHNldiA9PT0gXCJISUdIXCJcclxuICAgICAgICAgICAgICAgICAgICAgID8gXCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSB0ZXh0LXJlZC02MDAgZm9udC1ib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIDogXCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSB0ZXh0LXllbGxvdy02MDAgZm9udC1ib2xkXCJcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICB7c2V2ID09PSBcIkNSSVRJQ0FMXCIgJiYgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPn1cclxuICAgICAgICAgICAgICAgICAge3NldiA9PT0gXCJISUdIXCIgJiYgPEFsZXJ0T2N0YWdvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz59XHJcbiAgICAgICAgICAgICAgICAgIHtzZXYgPT09IFwiTUVESVVNXCIgJiYgPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+fVxyXG4gICAgICAgICAgICAgICAgICB7c2V2fVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibWwtNSBsaXN0LWRpc2MgdGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgICAgICB7Z3JvdXBlZFtzZXZdLm1hcCgodzogYW55LCBpOiBudW1iZXIpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpfT57dy5tZXNzYWdlfTwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvUG9wb3ZlckNvbnRlbnQ+XHJcbiAgICA8L1BvcG92ZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbldhcm5pbmdJY29uV2l0aFBvcG92ZXIuZGlzcGxheU5hbWUgPSBcIldhcm5pbmdJY29uV2l0aFBvcG92ZXJcIjtcclxuXHJcbi8vIC0tLSBOZXc6IE1hbmlmZXN0QWN0aW9uQnV0dG9uIHRvIGNvcnJlY3RseSBoYW5kbGUgZGlhbG9nIHN0YXRlIC0tLVxyXG5jb25zdCBNYW5pZmVzdEFjdGlvbkJ1dHRvbiA9ICh7XHJcbiAgVHJhY2tTaGVldCxcclxuICB1c2VyRGF0YSxcclxufToge1xyXG4gIFRyYWNrU2hlZXQ6IGFueTtcclxuICB1c2VyRGF0YTogYW55O1xyXG59KSA9PiB7XHJcbiAgY29uc3QgW2lzRGlhbG9nT3Blbiwgc2V0SXNEaWFsb2dPcGVuXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBtYW5pZmVzdERldGFpbFJlZiA9IFJlYWN0LnVzZVJlZjxDcmVhdGVNYW5pZmVzdERldGFpbFJlZiB8IG51bGw+KG51bGwpO1xyXG5cclxuICBjb25zdCBoYW5kbGVPcGVuRGlhbG9nID0gKCkgPT4ge1xyXG4gICAgc2V0SXNEaWFsb2dPcGVuKHRydWUpO1xyXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIG1hbmlmZXN0RGV0YWlsUmVmLmN1cnJlbnQ/LmZldGNoTWFuaWZlc3REZXRhaWxzKFRyYWNrU2hlZXQ/LmlkKTtcclxuICAgIH0sIDApO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8QnV0dG9uXHJcbiAgICAgICAgdmFyaWFudD1cImN1c3RvbUJ1dHRvblwiXHJcbiAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgY2FwaXRhbGl6ZSBoLTQgdy00IHRleHQtZ3JheS02MDBcIlxyXG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZU9wZW5EaWFsb2d9XHJcbiAgICAgID5cclxuICAgICAgICA8TWRDcmVhdGVOZXdGb2xkZXIgLz5cclxuICAgICAgPC9CdXR0b24+XHJcbiAgICAgIHtpc0RpYWxvZ09wZW4gJiYgKFxyXG4gICAgICAgIDxDcmVhdGVNYW5pZmVzdERldGFpbFxyXG4gICAgICAgICAgcmVmPXttYW5pZmVzdERldGFpbFJlZn1cclxuICAgICAgICAgIHRyYWNrU2hlZXRJZD17VHJhY2tTaGVldD8uaWR9XHJcbiAgICAgICAgICBpc0RpYWxvZ09wZW49e2lzRGlhbG9nT3Blbn1cclxuICAgICAgICAgIHNldElzRGlhbG9nT3Blbj17c2V0SXNEaWFsb2dPcGVufVxyXG4gICAgICAgICAgdXNlckRhdGE9e3VzZXJEYXRhfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5NYW5pZmVzdEFjdGlvbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiTWFuaWZlc3RBY3Rpb25CdXR0b25cIjtcclxuXHJcbmNvbnN0IENvbHVtbiA9IChcclxuICBwZXJtaXNzaW9uczogYW55LFxyXG4gIHNldERlbGV0ZWREYXRhOiBhbnksXHJcbiAgZGVsZXRlRGF0YTogYW55LFxyXG4gIGNhcnJpZXJEYXRhVXBkYXRlOiBhbnksXHJcbiAgY2xpZW50RGF0YVVwZGF0ZTogYW55LFxyXG4gIHVzZXJEYXRhLFxyXG4gIHtcclxuICAgIGN1c3RvbUZpZWxkc01hcCxcclxuICAgIHNob3dPcmNhQ29sdW1ucyxcclxuICAgIHNob3dMZWdyYW5kQ29sdW1ucyxcclxuICB9OiB7XHJcbiAgICBjdXN0b21GaWVsZHNNYXA6IGFueTtcclxuICAgIHNob3dPcmNhQ29sdW1ucz86IGJvb2xlYW47XHJcbiAgICBzaG93TGVncmFuZENvbHVtbnM/OiBib29sZWFuO1xyXG4gIH1cclxuKSA9PiB7XHJcbiAgY29uc3QgYmFzZUNvbHVtbnM6IENvbERlZltdID0gW1xyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJjbGllbnRcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJDbGllbnRcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5jbGllbnQ/LmNsaWVudF9uYW1lIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiY29tcGFueVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkNvbXBhbnlcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5jb21wYW55IHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiZGl2aXNpb25cIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJEaXZpc2lvblwiLFxyXG4gICAgICB2YWx1ZUdldHRlcjogKHBhcmFtcykgPT4gcGFyYW1zLmRhdGE/LmRpdmlzaW9uIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiY2FycmllclwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkNhcnJpZXJcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5jYXJyaWVyPy5uYW1lIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiZnRwRmlsZU5hbWVcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJGVFAgRmlsZSBOYW1lXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8uZnRwRmlsZU5hbWUgfHwgXCJOL0FcIixcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJmdHBQYWdlXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiRlRQIFBhZ2VcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5mdHBQYWdlIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiZmlsZVBhdGhcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJGaWxlIFBhdGhcIixcclxuICAgICAgY2VsbFJlbmRlcmVyOiAocGFyYW1zOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCBmaWxlUGF0aCA9IHBhcmFtcy5kYXRhPy5maWxlUGF0aDtcclxuICAgICAgICBjb25zdCBoYXNGaWxlUGF0aCA9IGZpbGVQYXRoICYmIGZpbGVQYXRoICE9PSBcIk4vQVwiO1xyXG4gICAgICAgIGNvbnN0IGRpc3BsYXlUZXh0ID0gaGFzRmlsZVBhdGggPyBmaWxlUGF0aCA6IFwiTm8gZmlsZSBwYXRoIGdlbmVyYXRlZFwiO1xyXG5cclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgaC1mdWxsIGdhcC0yIG1pbi13LTBcIj5cclxuICAgICAgICAgICAgPENvcHlCdXR0b25cclxuICAgICAgICAgICAgICB0ZXh0PXtoYXNGaWxlUGF0aCA/IGZpbGVQYXRoIDogXCJcIn1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17IWhhc0ZpbGVQYXRofVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgICAgICBgdHJ1bmNhdGUgcHItMiBtaW4tdy0wIGZsZXgtMSBgICtcclxuICAgICAgICAgICAgICAgIChoYXNGaWxlUGF0aFxyXG4gICAgICAgICAgICAgICAgICA/IFwidGV4dC1ibGFjayBmb250LW1vbm8gdGV4dC14c1wiXHJcbiAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNTAwIGl0YWxpYyB0ZXh0LXhzXCIpXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHRpdGxlPXtkaXNwbGF5VGV4dH1cclxuICAgICAgICAgICAgICBzdHlsZT17eyBtaW5XaWR0aDogMCB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge2Rpc3BsYXlUZXh0fVxyXG4gICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPjwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKTtcclxuICAgICAgfSxcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHtcclxuICAgICAgICBjb25zdCBmaWxlUGF0aCA9IHBhcmFtcy5kYXRhPy5maWxlUGF0aDtcclxuICAgICAgICBpZiAoIWZpbGVQYXRoIHx8IGZpbGVQYXRoID09PSBcIk4vQVwiKSB7XHJcbiAgICAgICAgICByZXR1cm4gXCJObyBmaWxlIHBhdGggZ2VuZXJhdGVkXCI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBmaWxlUGF0aDtcclxuICAgICAgfSxcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDU1MCxcclxuICAgICAgY2VsbFN0eWxlOiAoKSA9PiAoe1xyXG4gICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgcGFkZGluZzogXCI0cHggOHB4XCIsXHJcbiAgICAgICAgaGVpZ2h0OiBcIjEwMCVcIixcclxuICAgICAgICBib3JkZXJSaWdodDogXCIxcHggc29saWQgI2UwZTBlMFwiLFxyXG4gICAgICAgIHdoaXRlU3BhY2U6IFwibm93cmFwXCIsIC8vIFByZXZlbnQgdGV4dCB3cmFwcGluZ1xyXG4gICAgICAgIG92ZXJmbG93OiBcImhpZGRlblwiLCAvLyBIaWRlIG92ZXJmbG93XHJcbiAgICAgICAgdGV4dE92ZXJmbG93OiBcImVsbGlwc2lzXCIsIC8vIEFkZCBlbGxpcHNpcyBmb3Igb3ZlcmZsb3dlZCB0ZXh0XHJcbiAgICAgIH0pLFxyXG4gICAgICB0b29sdGlwVmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHtcclxuICAgICAgICBjb25zdCBmaWxlUGF0aCA9IHBhcmFtcy5kYXRhPy5maWxlUGF0aDtcclxuICAgICAgICBpZiAoIWZpbGVQYXRoIHx8IGZpbGVQYXRoID09PSBcIk4vQVwiKSB7XHJcbiAgICAgICAgICByZXR1cm4gXCJObyBmaWxlIHBhdGggZ2VuZXJhdGVkIC0gdGhpcyBlbnRyeSB3YXMgY3JlYXRlZCBiZWZvcmUgZmlsZSBwYXRoIGdlbmVyYXRpb24gd2FzIGltcGxlbWVudGVkXCI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBmaWxlUGF0aDtcclxuICAgICAgfSxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJNYXN0ZXJJbnZvaWNlXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiTWFzdGVyIEludm9pY2VcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5tYXN0ZXJJbnZvaWNlIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiaW52b2ljZVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkludm9pY2VcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5pbnZvaWNlIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiYm9sXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiQm9sXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8uYm9sIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwicmVjZWl2ZWREYXRlXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiUmVjZWl2ZWQgRGF0ZVwiLFxyXG4gICAgICB2YWx1ZUZvcm1hdHRlcjogKHBhcmFtcykgPT4ge1xyXG4gICAgICAgIGlmICghcGFyYW1zLnZhbHVlKSByZXR1cm4gXCJOL0FcIjtcclxuICAgICAgICBjb25zdCBkdCA9IERhdGVUaW1lLmZyb21JU08ocGFyYW1zLnZhbHVlLCB7IHpvbmU6IFwidXRjXCIgfSk7XHJcbiAgICAgICAgcmV0dXJuIGR0LmlzVmFsaWQgPyBkdC50b0Zvcm1hdChcImRkLU1NLXl5eXlcIikgOiBcIk4vQVwiO1xyXG4gICAgICB9LFxyXG4gICAgICBmaWx0ZXI6IFwiYWdEYXRlQ29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIGNvbXBhcmF0b3I6IChmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0OiBEYXRlLCBjZWxsVmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgICAgICAgaWYgKCFjZWxsVmFsdWUpIHJldHVybiAtMTtcclxuICAgICAgICAgIGNvbnN0IGNlbGxEYXRlID0gRGF0ZVRpbWUuZnJvbUlTTyhjZWxsVmFsdWUsIHsgem9uZTogXCJ1dGNcIiB9KVxyXG4gICAgICAgICAgICAuc3RhcnRPZihcImRheVwiKVxyXG4gICAgICAgICAgICAudG9KU0RhdGUoKTtcclxuICAgICAgICAgIGNvbnN0IGZpbHRlckRhdGUgPSBuZXcgRGF0ZShcclxuICAgICAgICAgICAgRGF0ZS5VVEMoXHJcbiAgICAgICAgICAgICAgZmlsdGVyTG9jYWxEYXRlQXRNaWRuaWdodC5nZXRGdWxsWWVhcigpLFxyXG4gICAgICAgICAgICAgIGZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQuZ2V0TW9udGgoKSxcclxuICAgICAgICAgICAgICBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LmdldERhdGUoKVxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICApO1xyXG4gICAgICAgICAgaWYgKGNlbGxEYXRlIDwgZmlsdGVyRGF0ZSkgcmV0dXJuIC0xO1xyXG4gICAgICAgICAgaWYgKGNlbGxEYXRlID4gZmlsdGVyRGF0ZSkgcmV0dXJuIDE7XHJcbiAgICAgICAgICByZXR1cm4gMDtcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJ1dHRvbnM6IFtcImNsZWFyXCJdLFxyXG4gICAgICB9LFxyXG4gICAgICB3aWR0aDogMTQwLFxyXG4gICAgICBoZWFkZXJDb21wb25lbnQ6IFBpbm5lZEhlYWRlcixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImludm9pY2VEYXRlXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiSW52b2ljZSBEYXRlXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZGF0ZSA9IHBhcmFtcy5kYXRhPy5pbnZvaWNlRGF0ZTtcclxuICAgICAgICByZXR1cm4gZGF0ZVxyXG4gICAgICAgICAgPyBEYXRlVGltZS5mcm9tSVNPKGRhdGUsIHsgem9uZTogXCJ1dGNcIiB9KS50b0Zvcm1hdChcImRkLU1NLXl5eXlcIilcclxuICAgICAgICAgIDogXCJOL0FcIjtcclxuICAgICAgfSxcclxuICAgICAgZmlsdGVyOiBcImFnRGF0ZUNvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBjb21wYXJhdG9yOiAoZmlsdGVyTG9jYWxEYXRlQXRNaWRuaWdodCwgY2VsbFZhbHVlKSA9PiB7XHJcbiAgICAgICAgICBpZiAoIWNlbGxWYWx1ZSkgcmV0dXJuIC0xO1xyXG4gICAgICAgICAgY29uc3QgY2VsbERhdGUgPSBEYXRlVGltZS5mcm9tSVNPKGNlbGxWYWx1ZSwgeyB6b25lOiBcInV0Y1wiIH0pXHJcbiAgICAgICAgICAgIC5zdGFydE9mKFwiZGF5XCIpXHJcbiAgICAgICAgICAgIC50b0pTRGF0ZSgpO1xyXG4gICAgICAgICAgY29uc3QgZmlsdGVyRGF0ZSA9IG5ldyBEYXRlKFxyXG4gICAgICAgICAgICBEYXRlLlVUQyhcclxuICAgICAgICAgICAgICBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LmdldEZ1bGxZZWFyKCksXHJcbiAgICAgICAgICAgICAgZmlsdGVyTG9jYWxEYXRlQXRNaWRuaWdodC5nZXRNb250aCgpLFxyXG4gICAgICAgICAgICAgIGZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQuZ2V0RGF0ZSgpXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgICBpZiAoY2VsbERhdGUgPCBmaWx0ZXJEYXRlKSByZXR1cm4gLTE7XHJcbiAgICAgICAgICBpZiAoY2VsbERhdGUgPiBmaWx0ZXJEYXRlKSByZXR1cm4gMTtcclxuICAgICAgICAgIHJldHVybiAwO1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwic2hpcG1lbnREYXRlXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiU2hpcG1lbnQgRGF0ZVwiLFxyXG4gICAgICB2YWx1ZUdldHRlcjogKHBhcmFtcykgPT4ge1xyXG4gICAgICAgIGNvbnN0IGRhdGUgPSBwYXJhbXMuZGF0YT8uc2hpcG1lbnREYXRlO1xyXG4gICAgICAgIHJldHVybiBkYXRlXHJcbiAgICAgICAgICA/IERhdGVUaW1lLmZyb21JU08oZGF0ZSwgeyB6b25lOiBcInV0Y1wiIH0pLnRvRm9ybWF0KFwiZGQtTU0teXl5eVwiKVxyXG4gICAgICAgICAgOiBcIk4vQVwiO1xyXG4gICAgICB9LFxyXG4gICAgICBmaWx0ZXI6IFwiYWdEYXRlQ29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIGNvbXBhcmF0b3I6IChmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LCBjZWxsVmFsdWUpID0+IHtcclxuICAgICAgICAgIGlmICghY2VsbFZhbHVlKSByZXR1cm4gLTE7XHJcbiAgICAgICAgICBjb25zdCBjZWxsRGF0ZSA9IERhdGVUaW1lLmZyb21JU08oY2VsbFZhbHVlLCB7IHpvbmU6IFwidXRjXCIgfSlcclxuICAgICAgICAgICAgLnN0YXJ0T2YoXCJkYXlcIilcclxuICAgICAgICAgICAgLnRvSlNEYXRlKCk7XHJcbiAgICAgICAgICBjb25zdCBmaWx0ZXJEYXRlID0gbmV3IERhdGUoXHJcbiAgICAgICAgICAgIERhdGUuVVRDKFxyXG4gICAgICAgICAgICAgIGZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQuZ2V0RnVsbFllYXIoKSxcclxuICAgICAgICAgICAgICBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LmdldE1vbnRoKCksXHJcbiAgICAgICAgICAgICAgZmlsdGVyTG9jYWxEYXRlQXRNaWRuaWdodC5nZXREYXRlKClcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgICAgIGlmIChjZWxsRGF0ZSA8IGZpbHRlckRhdGUpIHJldHVybiAtMTtcclxuICAgICAgICAgIGlmIChjZWxsRGF0ZSA+IGZpbHRlckRhdGUpIHJldHVybiAxO1xyXG4gICAgICAgICAgcmV0dXJuIDA7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJpbnZvaWNlVG90YWxcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJJbnZvaWNlIFRvdGFsXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8uaW52b2ljZVRvdGFsIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiY3VycmVuY3lcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJDdXJyZW5jeVwiLFxyXG4gICAgICB2YWx1ZUdldHRlcjogKHBhcmFtcykgPT4gcGFyYW1zLmRhdGE/LmN1cnJlbmN5IHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwicXR5U2hpcHBlZFwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIlF0eSBTaGlwcGVkXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8ucXR5U2hpcHBlZCB8fCBcIk4vQVwiLFxyXG4gICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIGJ1dHRvbnM6IFtcImNsZWFyXCJdLFxyXG4gICAgICB9LFxyXG4gICAgICB3aWR0aDogMTQwLFxyXG4gICAgICBoZWFkZXJDb21wb25lbnQ6IFBpbm5lZEhlYWRlcixcclxuICAgIH0sXHJcblxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJxdWFudGl0eUJpbGxlZFRleHRcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJRdWFudGl0eSBCaWxsZWRcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5xdWFudGl0eUJpbGxlZFRleHQgfHwgXCJOL0FcIixcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJpbnZvaWNlU3RhdHVzXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiSW52b2ljZSBTdGF0dXNcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5pbnZvaWNlU3RhdHVzIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwibWFudWFsTWF0Y2hpbmdcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJNYW51YWwgTWF0Y2hpbmdcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5tYW51YWxNYXRjaGluZyB8fCBcIk4vQVwiLFxyXG4gICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIGJ1dHRvbnM6IFtcImNsZWFyXCJdLFxyXG4gICAgICB9LFxyXG4gICAgICB3aWR0aDogMTQwLFxyXG4gICAgICBoZWFkZXJDb21wb25lbnQ6IFBpbm5lZEhlYWRlcixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImludm9pY2VUeXBlXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiSW52b2ljZSBUeXBlXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8uaW52b2ljZVR5cGUgfHwgXCJOL0FcIixcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJ3ZWlnaHRVbml0TmFtZVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIldlaWdodCBVbml0XCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8ud2VpZ2h0VW5pdE5hbWUgfHwgXCJOL0FcIixcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJzYXZpbmdzXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiU2F2aW5nc1wiLFxyXG4gICAgICB2YWx1ZUdldHRlcjogKHBhcmFtcykgPT4gcGFyYW1zLmRhdGE/LnNhdmluZ3MgfHwgXCJOL0FcIixcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJmcmVpZ2h0Q2xhc3NcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJGcmVpZ2h0IENsYXNzXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8uZnJlaWdodENsYXNzIHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiYmlsbFRvQ2xpZW50XCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiQmlsbCBUbyBDbGllbnRcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHtcclxuICAgICAgICBjb25zdCBiaWxsVG9DbGllbnQgPSBwYXJhbXMuZGF0YT8uYmlsbFRvQ2xpZW50O1xyXG4gICAgICAgIGlmIChiaWxsVG9DbGllbnQgPT09IHRydWUpIHJldHVybiBcIlllc1wiO1xyXG4gICAgICAgIGlmIChiaWxsVG9DbGllbnQgPT09IGZhbHNlKSByZXR1cm4gXCJOb1wiO1xyXG4gICAgICAgIHJldHVybiBcIk4vQVwiO1xyXG4gICAgICB9LFxyXG4gICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIGJ1dHRvbnM6IFtcImNsZWFyXCJdLFxyXG4gICAgICB9LFxyXG4gICAgICB3aWR0aDogMTQwLFxyXG4gICAgICBoZWFkZXJDb21wb25lbnQ6IFBpbm5lZEhlYWRlcixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcImRvY0F2YWlsYWJsZVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkRvYyBBdmFpbGFibGVcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5kb2NBdmFpbGFibGUgfHwgXCJOL0FcIixcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJub3Rlc1wiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIk5vdGVzXCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8ubm90ZXMgfHwgXCJOL0FcIixcclxuICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgICAgd2lkdGg6IDE0MCxcclxuICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJlbnRlcmVkQnlcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJFbnRlcmVkIGJ5XCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiBwYXJhbXMuZGF0YT8uZW50ZXJlZEJ5IHx8IFwiTi9BXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgfSxcclxuICBdO1xyXG5cclxuICBpZiAoY3VzdG9tRmllbGRzTWFwICYmIE9iamVjdC5rZXlzKGN1c3RvbUZpZWxkc01hcCkubGVuZ3RoID4gMCkge1xyXG4gICAgT2JqZWN0LmtleXMoY3VzdG9tRmllbGRzTWFwKS5mb3JFYWNoKChmaWVsZElkOiBzdHJpbmcpID0+IHtcclxuICAgICAgY29uc3QgZmllbGRNZXRhID0gY3VzdG9tRmllbGRzTWFwW2ZpZWxkSWRdO1xyXG4gICAgICBjb25zdCBmaWVsZE5hbWUgPSBmaWVsZE1ldGE/Lm5hbWUgfHwgYEN1c3RvbSBGaWVsZCAke2ZpZWxkSWR9YDtcclxuICAgICAgY29uc3QgZmllbGRUeXBlID0gZmllbGRNZXRhPy50eXBlO1xyXG4gICAgICBiYXNlQ29sdW1ucy5wdXNoKHtcclxuICAgICAgICBmaWVsZDogYGN1c3RvbUZpZWxkXyR7ZmllbGRJZH1gLFxyXG4gICAgICAgIGhlYWRlck5hbWU6IGZpZWxkTmFtZSxcclxuICAgICAgICB2YWx1ZUdldHRlcjogKHBhcmFtcykgPT4ge1xyXG4gICAgICAgICAgY29uc3QgdmFsdWUgPSBwYXJhbXMuZGF0YT8uY3VzdG9tRmllbGRzPy5bZmllbGRJZF07XHJcbiAgICAgICAgICBpZiAoIXZhbHVlKSByZXR1cm4gXCJOL0FcIjtcclxuICAgICAgICAgIGlmIChmaWVsZFR5cGUgPT09IFwiREFURVwiKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGR0ID0gRGF0ZVRpbWUuZnJvbUlTTyh2YWx1ZSwgeyB6b25lOiBcInV0Y1wiIH0pO1xyXG4gICAgICAgICAgICByZXR1cm4gZHQuaXNWYWxpZCA/IGR0LnRvRm9ybWF0KFwiZGQtTU0teXl5eVwiKSA6IHZhbHVlO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICAgIGZpbHRlclBhcmFtczogeyBidXR0b25zOiBbXCJjbGVhclwiXSB9LFxyXG4gICAgICAgIHdpZHRoOiAxNDAsXHJcbiAgICAgICAgaGVhZGVyQ29tcG9uZW50OiBQaW5uZWRIZWFkZXIsXHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvLyBBZGQgbWFuaWZlc3QgZmllbGRzIGFmdGVyIGN1c3RvbSBmaWVsZHNcclxuICBpZiAoc2hvd09yY2FDb2x1bW5zKSB7XHJcbiAgICBiYXNlQ29sdW1ucy5wdXNoKFxyXG4gICAgICB7XHJcbiAgICAgICAgZmllbGQ6IFwibWFuaWZlc3RTdGF0dXNcIixcclxuICAgICAgICBoZWFkZXJOYW1lOiBcIk9SQ0EgU1RBVFVTXCIsXHJcbiAgICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+XHJcbiAgICAgICAgICBwYXJhbXMuZGF0YT8ubWFuaWZlc3REZXRhaWxzPy5tYW5pZmVzdFN0YXR1cyB8fCBcIk4vQVwiLFxyXG4gICAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgICBmaWx0ZXJQYXJhbXM6IHsgYnV0dG9uczogW1wiY2xlYXJcIl0gfSxcclxuICAgICAgICB3aWR0aDogMTQwLFxyXG4gICAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgZmllbGQ6IFwibWFuaWZlc3REYXRlXCIsXHJcbiAgICAgICAgaGVhZGVyTmFtZTogXCJSRVZJRVcgREFURVwiLFxyXG4gICAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PiB7XHJcbiAgICAgICAgICBjb25zdCBkYXRlID0gcGFyYW1zLmRhdGE/Lm1hbmlmZXN0RGV0YWlscz8ubWFuaWZlc3REYXRlO1xyXG4gICAgICAgICAgcmV0dXJuIGRhdGVcclxuICAgICAgICAgICAgPyBEYXRlVGltZS5mcm9tSVNPKGRhdGUsIHsgem9uZTogXCJ1dGNcIiB9KS50b0Zvcm1hdChcImRkLU1NLXl5eXlcIilcclxuICAgICAgICAgICAgOiBcIk4vQVwiO1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgZmlsdGVyOiBcImFnRGF0ZUNvbHVtbkZpbHRlclwiLFxyXG4gICAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgICAgY29tcGFyYXRvcjogKGZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQsIGNlbGxWYWx1ZSkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoIWNlbGxWYWx1ZSkgcmV0dXJuIC0xO1xyXG4gICAgICAgICAgICBjb25zdCBjZWxsRGF0ZSA9IERhdGVUaW1lLmZyb21JU08oY2VsbFZhbHVlLCB7IHpvbmU6IFwidXRjXCIgfSlcclxuICAgICAgICAgICAgICAuc3RhcnRPZihcImRheVwiKVxyXG4gICAgICAgICAgICAgIC50b0pTRGF0ZSgpO1xyXG4gICAgICAgICAgICBjb25zdCBmaWx0ZXJEYXRlID0gbmV3IERhdGUoXHJcbiAgICAgICAgICAgICAgRGF0ZS5VVEMoXHJcbiAgICAgICAgICAgICAgICBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LmdldEZ1bGxZZWFyKCksXHJcbiAgICAgICAgICAgICAgICBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LmdldE1vbnRoKCksXHJcbiAgICAgICAgICAgICAgICBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LmdldERhdGUoKVxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgaWYgKGNlbGxEYXRlIDwgZmlsdGVyRGF0ZSkgcmV0dXJuIC0xO1xyXG4gICAgICAgICAgICBpZiAoY2VsbERhdGUgPiBmaWx0ZXJEYXRlKSByZXR1cm4gMTtcclxuICAgICAgICAgICAgcmV0dXJuIDA7XHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB3aWR0aDogMTQwLFxyXG4gICAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgZmllbGQ6IFwiYWN0aW9uUmVxdWlyZWRcIixcclxuICAgICAgICBoZWFkZXJOYW1lOiBcIkFDVElPTiBSRVFVSVJFRCBGUk9NXCIsXHJcbiAgICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+XHJcbiAgICAgICAgICBwYXJhbXMuZGF0YT8ubWFuaWZlc3REZXRhaWxzPy5hY3Rpb25SZXF1aXJlZCB8fCBcIk4vQVwiLFxyXG4gICAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgICBmaWx0ZXJQYXJhbXM6IHsgYnV0dG9uczogW1wiY2xlYXJcIl0gfSxcclxuICAgICAgICB3aWR0aDogMTQwLFxyXG4gICAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgZmllbGQ6IFwibWFuaWZlc3ROb3Rlc1wiLFxyXG4gICAgICAgIGhlYWRlck5hbWU6IFwiT1JDQSBDT01NRU5UU1wiLFxyXG4gICAgICAgIHZhbHVlR2V0dGVyOiAocGFyYW1zKSA9PlxyXG4gICAgICAgICAgcGFyYW1zLmRhdGE/Lm1hbmlmZXN0RGV0YWlscz8ubWFuaWZlc3ROb3RlcyB8fCBcIk4vQVwiLFxyXG4gICAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgICBmaWx0ZXJQYXJhbXM6IHsgYnV0dG9uczogW1wiY2xlYXJcIl0gfSxcclxuICAgICAgICB3aWR0aDogMTgwLFxyXG4gICAgICAgIGhlYWRlckNvbXBvbmVudDogUGlubmVkSGVhZGVyLFxyXG4gICAgICB9XHJcbiAgICApO1xyXG4gIH1cclxuICBpZiAoc2hvd0xlZ3JhbmRDb2x1bW5zKSB7XHJcbiAgICBiYXNlQ29sdW1ucy5wdXNoKHtcclxuICAgICAgZmllbGQ6IFwiZnJlaWdodFRlcm1cIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJGUkVJR0hUIFRFUk1cIixcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IHBhcmFtcy5kYXRhPy5mcmVpZ2h0VGVybSB8fCBcIk4vQVwiLFxyXG4gICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczogeyBidXR0b25zOiBbXCJjbGVhclwiXSB9LFxyXG4gICAgICB3aWR0aDogMTQwLFxyXG4gICAgICBoZWFkZXJDb21wb25lbnQ6IFBpbm5lZEhlYWRlcixcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLy8gQWRkIGFjdGlvbiBjb2x1bW4gYWZ0ZXIgY3VzdG9tIGZpZWxkc1xyXG4gIGJhc2VDb2x1bW5zLnB1c2goe1xyXG4gICAgZmllbGQ6IFwid2FybmluZ3NcIixcclxuICAgIGhlYWRlck5hbWU6IFwiV2FybmluZ3NcIixcclxuICAgIGNlbGxSZW5kZXJlcjogKHBhcmFtczogYW55KSA9PiB7XHJcbiAgICAgIGNvbnN0IHdhcm5pbmdzID0gcGFyYW1zPy5kYXRhPy5zeXN0ZW1HZW5lcmF0ZWRXYXJuaW5ncyB8fCBbXTtcclxuICAgICAgcmV0dXJuIDxXYXJuaW5nSWNvbldpdGhQb3BvdmVyIHdhcm5pbmdzPXt3YXJuaW5nc30gLz47XHJcbiAgICB9LFxyXG4gICAgd2lkdGg6IDEwMCxcclxuICAgIHNvcnRhYmxlOiBmYWxzZSxcclxuICAgIGNlbGxTdHlsZTogeyBkaXNwbGF5OiBcImZsZXhcIiwganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsIGFsaWduSXRlbXM6IFwiY2VudGVyXCIgfSxcclxuICB9KTtcclxuXHJcbiAgYmFzZUNvbHVtbnMucHVzaCh7XHJcbiAgICBmaWVsZDogXCJhY3Rpb25cIixcclxuICAgIGhlYWRlck5hbWU6IFwiQWN0aW9uXCIsXHJcbiAgICBjZWxsUmVuZGVyZXI6IChwYXJhbXM6IGFueSkgPT4ge1xyXG4gICAgICBjb25zdCBUcmFja1NoZWV0ID0gcGFyYW1zPy5kYXRhO1xyXG4gICAgICBjb25zdCB3YXJuaW5ncyA9IFRyYWNrU2hlZXQ/LnN5c3RlbUdlbmVyYXRlZFdhcm5pbmdzIHx8IFtdO1xyXG5cclxuICAgICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICB7LyogT25seSBzaG93IENyZWF0ZSBNYW5pZmVzdCBidXR0b24gaWYgb3JjYSBjb2x1bW5zIGFyZSB2aXNpYmxlICovfVxyXG4gICAgICAgICAge3Nob3dPcmNhQ29sdW1ucyAmJiAoXHJcbiAgICAgICAgICAgIDxNYW5pZmVzdEFjdGlvbkJ1dHRvbiBUcmFja1NoZWV0PXtUcmFja1NoZWV0fSB1c2VyRGF0YT17dXNlckRhdGF9IC8+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAgPFVwZGF0ZVRyYWNrU2hlZXRcclxuICAgICAgICAgICAgdHJhY2tTaGVldD17VHJhY2tTaGVldH1cclxuICAgICAgICAgICAgY2xpZW50RGF0YVVwZGF0ZT17Y2xpZW50RGF0YVVwZGF0ZX1cclxuICAgICAgICAgICAgY2FycmllckRhdGFVcGRhdGU9e2NhcnJpZXJEYXRhVXBkYXRlfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxQZXJtaXNzaW9uV3JhcHBlclxyXG4gICAgICAgICAgICBwZXJtaXNzaW9ucz17cGVybWlzc2lvbnN9XHJcbiAgICAgICAgICAgIHJlcXVpcmVkUGVybWlzc2lvbnM9e1tcImRlbGV0ZS10cmFja1NoZWV0XCJdfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8RGVsZXRlUm93XHJcbiAgICAgICAgICAgICAgcm91dGU9e2Ake3RyYWNrU2hlZXRzX3JvdXRlcy5ERUxFVEVfVFJBQ0tfU0hFRVRTfS8ke1RyYWNrU2hlZXQ/LmlkfWB9XHJcbiAgICAgICAgICAgICAgb25TdWNjZXNzPXsoKSA9PiBzZXREZWxldGVkRGF0YSghZGVsZXRlRGF0YSl9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L1Blcm1pc3Npb25XcmFwcGVyPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApO1xyXG4gICAgfSxcclxuICAgIHNvcnRhYmxlOiBmYWxzZSxcclxuICAgIHdpZHRoOiAxMDAsXHJcbiAgICBwaW5uZWQ6IFwicmlnaHRcIixcclxuICAgIGNlbGxTdHlsZTogKCkgPT4gKHtcclxuICAgICAgZm9udEZhbWlseTogXCJpbmhlcml0XCIsXHJcbiAgICAgIHRleHRPdmVyZmxvdzogXCJjbGlwXCIsXHJcbiAgICAgIGNvbG9yOiBcImluaGVyaXRcIixcclxuICAgICAgZm9udFN0eWxlOiBcIm5vcm1hbFwiLFxyXG4gICAgfSksXHJcbiAgfSk7XHJcblxyXG4gIHJldHVybiBiYXNlQ29sdW1ucztcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IENvbHVtbjtcclxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi8vKiBjOCBpZ25vcmUgc3RhcnQgKi8vKiBlc2xpbnQtZGlzYWJsZSAqLztmdW5jdGlvbiBvb19jbSgpe3RyeXtyZXR1cm4gKDAsZXZhbCkoXCJnbG9iYWxUaGlzLl9jb25zb2xlX25pbmphXCIpIHx8ICgwLGV2YWwpKFwiLyogaHR0cHM6Ly9naXRodWIuY29tL3dhbGxhYnlqcy9jb25zb2xlLW5pbmphI2hvdy1kb2VzLWl0LXdvcmsgKi8ndXNlIHN0cmljdCc7dmFyIF8weDI1MjFlZD1fMHgyMmQ0OyhmdW5jdGlvbihfMHgxNWU4MjQsXzB4NTJlNGRlKXt2YXIgXzB4M2ZjZDUxPV8weDIyZDQsXzB4MTUxMGU0PV8weDE1ZTgyNCgpO3doaWxlKCEhW10pe3RyeXt2YXIgXzB4MzMwNTBkPXBhcnNlSW50KF8weDNmY2Q1MSgweDEyNykpLzB4MSoocGFyc2VJbnQoXzB4M2ZjZDUxKDB4YjUpKS8weDIpK3BhcnNlSW50KF8weDNmY2Q1MSgweDExYykpLzB4MyooLXBhcnNlSW50KF8weDNmY2Q1MSgweDE4MCkpLzB4NCkrcGFyc2VJbnQoXzB4M2ZjZDUxKDB4ZDIpKS8weDUrcGFyc2VJbnQoXzB4M2ZjZDUxKDB4MTUxKSkvMHg2KigtcGFyc2VJbnQoXzB4M2ZjZDUxKDB4MTY0KSkvMHg3KStwYXJzZUludChfMHgzZmNkNTEoMHgxNjEpKS8weDgrLXBhcnNlSW50KF8weDNmY2Q1MSgweGQzKSkvMHg5KihwYXJzZUludChfMHgzZmNkNTEoMHgxMDgpKS8weGEpK3BhcnNlSW50KF8weDNmY2Q1MSgweDEyOSkpLzB4YjtpZihfMHgzMzA1MGQ9PT1fMHg1MmU0ZGUpYnJlYWs7ZWxzZSBfMHgxNTEwZTRbJ3B1c2gnXShfMHgxNTEwZTRbJ3NoaWZ0J10oKSk7fWNhdGNoKF8weDU4NWEyNyl7XzB4MTUxMGU0WydwdXNoJ10oXzB4MTUxMGU0WydzaGlmdCddKCkpO319fShfMHgxYmFlLDB4ZGU0ZmQpKTt2YXIgRz1PYmplY3RbXzB4MjUyMWVkKDB4MTc3KV0sVj1PYmplY3RbJ2RlZmluZVByb3BlcnR5J10sZWU9T2JqZWN0W18weDI1MjFlZCgweGQ4KV0sdGU9T2JqZWN0W18weDI1MjFlZCgweDE1ZSldLG5lPU9iamVjdFsnZ2V0UHJvdG90eXBlT2YnXSxyZT1PYmplY3RbJ3Byb3RvdHlwZSddW18weDI1MjFlZCgweDk0KV0saWU9KF8weDE1YTQ5MyxfMHgxZGE0ZTUsXzB4MzhlOTk4LF8weDE5MTc4OSk9Pnt2YXIgXzB4NWFhNGRhPV8weDI1MjFlZDtpZihfMHgxZGE0ZTUmJnR5cGVvZiBfMHgxZGE0ZTU9PV8weDVhYTRkYSgweDExMCl8fHR5cGVvZiBfMHgxZGE0ZTU9PV8weDVhYTRkYSgweDE0NCkpe2ZvcihsZXQgXzB4MTg5OGM5IG9mIHRlKF8weDFkYTRlNSkpIXJlW18weDVhYTRkYSgweGUxKV0oXzB4MTVhNDkzLF8weDE4OThjOSkmJl8weDE4OThjOSE9PV8weDM4ZTk5OCYmVihfMHgxNWE0OTMsXzB4MTg5OGM5LHsnZ2V0JzooKT0+XzB4MWRhNGU1W18weDE4OThjOV0sJ2VudW1lcmFibGUnOiEoXzB4MTkxNzg5PWVlKF8weDFkYTRlNSxfMHgxODk4YzkpKXx8XzB4MTkxNzg5W18weDVhYTRkYSgweGE4KV19KTt9cmV0dXJuIF8weDE1YTQ5Mzt9LGo9KF8weDM0ZWNjZixfMHgzZDU0N2MsXzB4MTI5NjY1KT0+KF8weDEyOTY2NT1fMHgzNGVjY2YhPW51bGw/RyhuZShfMHgzNGVjY2YpKTp7fSxpZShfMHgzZDU0N2N8fCFfMHgzNGVjY2Z8fCFfMHgzNGVjY2ZbXzB4MjUyMWVkKDB4MTYwKV0/VihfMHgxMjk2NjUsXzB4MjUyMWVkKDB4MTA2KSx7J3ZhbHVlJzpfMHgzNGVjY2YsJ2VudW1lcmFibGUnOiEweDB9KTpfMHgxMjk2NjUsXzB4MzRlY2NmKSkscT1jbGFzc3tjb25zdHJ1Y3RvcihfMHgzNjFjZTAsXzB4Y2RjNzYsXzB4MTg2MWViLF8weDEzNWMzOCxfMHg1NmQwODUsXzB4MjRlMmQxKXt2YXIgXzB4NGEzMmNmPV8weDI1MjFlZCxfMHgzNjIwNWIsXzB4MTc2YjJlLF8weDFlNDUzMSxfMHgyNDc2NTM7dGhpc1tfMHg0YTMyY2YoMHgxNjcpXT1fMHgzNjFjZTAsdGhpc1tfMHg0YTMyY2YoMHhlYSldPV8weGNkYzc2LHRoaXNbXzB4NGEzMmNmKDB4MTg5KV09XzB4MTg2MWViLHRoaXNbXzB4NGEzMmNmKDB4MTNiKV09XzB4MTM1YzM4LHRoaXNbXzB4NGEzMmNmKDB4OWQpXT1fMHg1NmQwODUsdGhpc1tfMHg0YTMyY2YoMHgxNGYpXT1fMHgyNGUyZDEsdGhpc1tfMHg0YTMyY2YoMHgxOGMpXT0hMHgwLHRoaXNbXzB4NGEzMmNmKDB4MTg1KV09ITB4MCx0aGlzWydfY29ubmVjdGVkJ109ITB4MSx0aGlzWydfY29ubmVjdGluZyddPSEweDEsdGhpc1tfMHg0YTMyY2YoMHhmMSldPSgoXzB4MTc2YjJlPShfMHgzNjIwNWI9XzB4MzYxY2UwW18weDRhMzJjZigweDE1MCldKT09bnVsbD92b2lkIDB4MDpfMHgzNjIwNWJbXzB4NGEzMmNmKDB4MTEzKV0pPT1udWxsP3ZvaWQgMHgwOl8weDE3NmIyZVsnTkVYVF9SVU5USU1FJ10pPT09XzB4NGEzMmNmKDB4MTA3KSx0aGlzW18weDRhMzJjZigweDExNCldPSEoKF8weDI0NzY1Mz0oXzB4MWU0NTMxPXRoaXNbXzB4NGEzMmNmKDB4MTY3KV1bXzB4NGEzMmNmKDB4MTUwKV0pPT1udWxsP3ZvaWQgMHgwOl8weDFlNDUzMVsndmVyc2lvbnMnXSkhPW51bGwmJl8weDI0NzY1M1tfMHg0YTMyY2YoMHgxODIpXSkmJiF0aGlzW18weDRhMzJjZigweGYxKV0sdGhpc1tfMHg0YTMyY2YoMHgxM2MpXT1udWxsLHRoaXNbJ19jb25uZWN0QXR0ZW1wdENvdW50J109MHgwLHRoaXNbJ19tYXhDb25uZWN0QXR0ZW1wdENvdW50J109MHgxNCx0aGlzW18weDRhMzJjZigweDEzOCldPV8weDRhMzJjZigweDkwKSx0aGlzW18weDRhMzJjZigweDE3ZCldPSh0aGlzW18weDRhMzJjZigweDExNCldP18weDRhMzJjZigweGNiKTpfMHg0YTMyY2YoMHgxM2YpKSt0aGlzW18weDRhMzJjZigweDEzOCldO31hc3luY1snZ2V0V2ViU29ja2V0Q2xhc3MnXSgpe3ZhciBfMHgzYzNhNGE9XzB4MjUyMWVkLF8weDNmN2MwZixfMHg0MDdkNWE7aWYodGhpc1tfMHgzYzNhNGEoMHgxM2MpXSlyZXR1cm4gdGhpc1tfMHgzYzNhNGEoMHgxM2MpXTtsZXQgXzB4MjYxNjMwO2lmKHRoaXNbJ19pbkJyb3dzZXInXXx8dGhpc1tfMHgzYzNhNGEoMHhmMSldKV8weDI2MTYzMD10aGlzW18weDNjM2E0YSgweDE2NyldW18weDNjM2E0YSgweGU3KV07ZWxzZXtpZigoXzB4M2Y3YzBmPXRoaXNbXzB4M2MzYTRhKDB4MTY3KV1bXzB4M2MzYTRhKDB4MTUwKV0pIT1udWxsJiZfMHgzZjdjMGZbXzB4M2MzYTRhKDB4MTFhKV0pXzB4MjYxNjMwPShfMHg0MDdkNWE9dGhpc1snZ2xvYmFsJ11bJ3Byb2Nlc3MnXSk9PW51bGw/dm9pZCAweDA6XzB4NDA3ZDVhW18weDNjM2E0YSgweDExYSldO2Vsc2UgdHJ5e2xldCBfMHgxYmM0YzY9YXdhaXQgaW1wb3J0KF8weDNjM2E0YSgweDE2YykpO18weDI2MTYzMD0oYXdhaXQgaW1wb3J0KChhd2FpdCBpbXBvcnQoXzB4M2MzYTRhKDB4ZGYpKSlbXzB4M2MzYTRhKDB4YzQpXShfMHgxYmM0YzZbXzB4M2MzYTRhKDB4MTQ4KV0odGhpc1tfMHgzYzNhNGEoMHgxM2IpXSxfMHgzYzNhNGEoMHgxMmYpKSlbXzB4M2MzYTRhKDB4MTJhKV0oKSkpW18weDNjM2E0YSgweDEwNildO31jYXRjaHt0cnl7XzB4MjYxNjMwPXJlcXVpcmUocmVxdWlyZSgncGF0aCcpW18weDNjM2E0YSgweDE0OCldKHRoaXNbXzB4M2MzYTRhKDB4MTNiKV0sJ3dzJykpO31jYXRjaHt0aHJvdyBuZXcgRXJyb3IoXzB4M2MzYTRhKDB4YTApKTt9fX1yZXR1cm4gdGhpc1snX1dlYlNvY2tldENsYXNzJ109XzB4MjYxNjMwLF8weDI2MTYzMDt9WydfY29ubmVjdFRvSG9zdE5vdyddKCl7dmFyIF8weDI0YWZmZD1fMHgyNTIxZWQ7dGhpc1tfMHgyNGFmZmQoMHhkNCldfHx0aGlzW18weDI0YWZmZCgweGIzKV18fHRoaXNbXzB4MjRhZmZkKDB4MTY2KV0+PXRoaXNbXzB4MjRhZmZkKDB4YjIpXXx8KHRoaXNbJ19hbGxvd2VkVG9Db25uZWN0T25TZW5kJ109ITB4MSx0aGlzWydfY29ubmVjdGluZyddPSEweDAsdGhpc1tfMHgyNGFmZmQoMHgxNjYpXSsrLHRoaXNbXzB4MjRhZmZkKDB4YWEpXT1uZXcgUHJvbWlzZSgoXzB4MWMxM2JhLF8weDU0MmEwNik9Pnt2YXIgXzB4M2MyOTQ4PV8weDI0YWZmZDt0aGlzW18weDNjMjk0OCgweDE2ZCldKClbXzB4M2MyOTQ4KDB4MTIxKV0oXzB4MTRkOGVhPT57dmFyIF8weDkzYzAwMz1fMHgzYzI5NDg7bGV0IF8weDM3NTg5MD1uZXcgXzB4MTRkOGVhKF8weDkzYzAwMygweDEyYikrKCF0aGlzW18weDkzYzAwMygweDExNCldJiZ0aGlzW18weDkzYzAwMygweDlkKV0/XzB4OTNjMDAzKDB4MThkKTp0aGlzW18weDkzYzAwMygweGVhKV0pKyc6Jyt0aGlzW18weDkzYzAwMygweDE4OSldKTtfMHgzNzU4OTBbXzB4OTNjMDAzKDB4MTI4KV09KCk9Pnt2YXIgXzB4NTc1NmFmPV8weDkzYzAwMzt0aGlzWydfYWxsb3dlZFRvU2VuZCddPSEweDEsdGhpc1tfMHg1NzU2YWYoMHgxNzEpXShfMHgzNzU4OTApLHRoaXNbXzB4NTc1NmFmKDB4MTBjKV0oKSxfMHg1NDJhMDYobmV3IEVycm9yKCdsb2dnZXJcXFxceDIwd2Vic29ja2V0XFxcXHgyMGVycm9yJykpO30sXzB4Mzc1ODkwW18weDkzYzAwMygweGM2KV09KCk9Pnt2YXIgXzB4NDljYzQ4PV8weDkzYzAwMzt0aGlzW18weDQ5Y2M0OCgweDExNCldfHxfMHgzNzU4OTBbXzB4NDljYzQ4KDB4MTQwKV0mJl8weDM3NTg5MFtfMHg0OWNjNDgoMHgxNDApXVsndW5yZWYnXSYmXzB4Mzc1ODkwW18weDQ5Y2M0OCgweDE0MCldW18weDQ5Y2M0OCgweGYzKV0oKSxfMHgxYzEzYmEoXzB4Mzc1ODkwKTt9LF8weDM3NTg5MFtfMHg5M2MwMDMoMHgxODcpXT0oKT0+e3ZhciBfMHgyMzQ2M2E9XzB4OTNjMDAzO3RoaXNbXzB4MjM0NjNhKDB4MTg1KV09ITB4MCx0aGlzW18weDIzNDYzYSgweDE3MSldKF8weDM3NTg5MCksdGhpc1snX2F0dGVtcHRUb1JlY29ubmVjdFNob3J0bHknXSgpO30sXzB4Mzc1ODkwW18weDkzYzAwMygweGM5KV09XzB4NzVhNTJlPT57dmFyIF8weDM5NTFlZT1fMHg5M2MwMDM7dHJ5e2lmKCEoXzB4NzVhNTJlIT1udWxsJiZfMHg3NWE1MmVbXzB4Mzk1MWVlKDB4YTMpXSl8fCF0aGlzW18weDM5NTFlZSgweDE0ZildKXJldHVybjtsZXQgXzB4NzhkNTE1PUpTT05bXzB4Mzk1MWVlKDB4MTExKV0oXzB4NzVhNTJlW18weDM5NTFlZSgweGEzKV0pO3RoaXNbXzB4Mzk1MWVlKDB4MTRmKV0oXzB4NzhkNTE1W18weDM5NTFlZSgweGVjKV0sXzB4NzhkNTE1W18weDM5NTFlZSgweDE1NyldLHRoaXNbJ2dsb2JhbCddLHRoaXNbXzB4Mzk1MWVlKDB4MTE0KV0pO31jYXRjaHt9fTt9KVtfMHgzYzI5NDgoMHgxMjEpXShfMHg1MWRmYjE9Pih0aGlzWydfY29ubmVjdGVkJ109ITB4MCx0aGlzW18weDNjMjk0OCgweGQ0KV09ITB4MSx0aGlzW18weDNjMjk0OCgweDE4NSldPSEweDEsdGhpc1tfMHgzYzI5NDgoMHgxOGMpXT0hMHgwLHRoaXNbJ19jb25uZWN0QXR0ZW1wdENvdW50J109MHgwLF8weDUxZGZiMSkpW18weDNjMjk0OCgweGMwKV0oXzB4YjQ1NjVjPT4odGhpc1tfMHgzYzI5NDgoMHhiMyldPSEweDEsdGhpc1tfMHgzYzI5NDgoMHhkNCldPSEweDEsY29uc29sZVtfMHgzYzI5NDgoMHgxODQpXShfMHgzYzI5NDgoMHhiNCkrdGhpc1tfMHgzYzI5NDgoMHgxMzgpXSksXzB4NTQyYTA2KG5ldyBFcnJvcihfMHgzYzI5NDgoMHhkYykrKF8weGI0NTY1YyYmXzB4YjQ1NjVjWydtZXNzYWdlJ10pKSkpKTt9KSk7fVtfMHgyNTIxZWQoMHgxNzEpXShfMHgyYmJmZjEpe3ZhciBfMHg3YTcyMjQ9XzB4MjUyMWVkO3RoaXNbXzB4N2E3MjI0KDB4YjMpXT0hMHgxLHRoaXNbXzB4N2E3MjI0KDB4ZDQpXT0hMHgxO3RyeXtfMHgyYmJmZjFbJ29uY2xvc2UnXT1udWxsLF8weDJiYmZmMVtfMHg3YTcyMjQoMHgxMjgpXT1udWxsLF8weDJiYmZmMVsnb25vcGVuJ109bnVsbDt9Y2F0Y2h7fXRyeXtfMHgyYmJmZjFbXzB4N2E3MjI0KDB4YzIpXTwweDImJl8weDJiYmZmMVtfMHg3YTcyMjQoMHgxMTYpXSgpO31jYXRjaHt9fVtfMHgyNTIxZWQoMHgxMGMpXSgpe3ZhciBfMHg4MjIyNDA9XzB4MjUyMWVkO2NsZWFyVGltZW91dCh0aGlzW18weDgyMjI0MCgweDE4MSldKSwhKHRoaXNbXzB4ODIyMjQwKDB4MTY2KV0+PXRoaXNbXzB4ODIyMjQwKDB4YjIpXSkmJih0aGlzWydfcmVjb25uZWN0VGltZW91dCddPXNldFRpbWVvdXQoKCk9Pnt2YXIgXzB4NTcyYWI0PV8weDgyMjI0MCxfMHg1OWEyOTk7dGhpc1tfMHg1NzJhYjQoMHhiMyldfHx0aGlzWydfY29ubmVjdGluZyddfHwodGhpc1tfMHg1NzJhYjQoMHgxNmUpXSgpLChfMHg1OWEyOTk9dGhpc1tfMHg1NzJhYjQoMHhhYSldKT09bnVsbHx8XzB4NTlhMjk5W18weDU3MmFiNCgweGMwKV0oKCk9PnRoaXNbXzB4NTcyYWI0KDB4MTBjKV0oKSkpO30sMHgxZjQpLHRoaXNbXzB4ODIyMjQwKDB4MTgxKV1bJ3VucmVmJ10mJnRoaXNbXzB4ODIyMjQwKDB4MTgxKV1bXzB4ODIyMjQwKDB4ZjMpXSgpKTt9YXN5bmNbXzB4MjUyMWVkKDB4YmQpXShfMHg0YjE1Y2Qpe3ZhciBfMHgzNjJkNmU9XzB4MjUyMWVkO3RyeXtpZighdGhpc1snX2FsbG93ZWRUb1NlbmQnXSlyZXR1cm47dGhpc1tfMHgzNjJkNmUoMHgxODUpXSYmdGhpc1tfMHgzNjJkNmUoMHgxNmUpXSgpLChhd2FpdCB0aGlzWydfd3MnXSlbXzB4MzYyZDZlKDB4YmQpXShKU09OW18weDM2MmQ2ZSgweGQ1KV0oXzB4NGIxNWNkKSk7fWNhdGNoKF8weDE1MjQ3Myl7dGhpc1tfMHgzNjJkNmUoMHhiZildP2NvbnNvbGVbXzB4MzYyZDZlKDB4MTg0KV0odGhpc1tfMHgzNjJkNmUoMHgxN2QpXSsnOlxcXFx4MjAnKyhfMHgxNTI0NzMmJl8weDE1MjQ3M1snbWVzc2FnZSddKSk6KHRoaXNbXzB4MzYyZDZlKDB4YmYpXT0hMHgwLGNvbnNvbGVbXzB4MzYyZDZlKDB4MTg0KV0odGhpc1snX3NlbmRFcnJvck1lc3NhZ2UnXSsnOlxcXFx4MjAnKyhfMHgxNTI0NzMmJl8weDE1MjQ3M1tfMHgzNjJkNmUoMHgxMmQpXSksXzB4NGIxNWNkKSksdGhpc1snX2FsbG93ZWRUb1NlbmQnXT0hMHgxLHRoaXNbXzB4MzYyZDZlKDB4MTBjKV0oKTt9fX07ZnVuY3Rpb24gXzB4MWJhZSgpe3ZhciBfMHg0NzU1MTc9Wyd1bmtub3duJywnX2FkZEZ1bmN0aW9uc05vZGUnLCdmYWlsZWRcXFxceDIwdG9cXFxceDIwZmluZFxcXFx4MjBhbmRcXFxceDIwbG9hZFxcXFx4MjBXZWJTb2NrZXQnLCdyZXBsYWNlJywncGFyZW50JywnZGF0YScsJ1tvYmplY3RcXFxceDIwU2V0XScsJ2F1dG9FeHBhbmRQcmV2aW91c09iamVjdHMnLCdfc2V0Tm9kZUV4cHJlc3Npb25QYXRoJywndmFsdWUnLCdlbnVtZXJhYmxlJywnYXV0b0V4cGFuZFByb3BlcnR5Q291bnQnLCdfd3MnLCdfc2V0Tm9kZVF1ZXJ5UGF0aCcsJ25leHQuanMnLCd0cmFjZScsJ19wX2xlbmd0aCcsJ2RlcHRoJywnNTYwMDgnLCdwdXNoJywnX21heENvbm5lY3RBdHRlbXB0Q291bnQnLCdfY29ubmVjdGVkJywnbG9nZ2VyXFxcXHgyMGZhaWxlZFxcXFx4MjB0b1xcXFx4MjBjb25uZWN0XFxcXHgyMHRvXFxcXHgyMGhvc3QsXFxcXHgyMHNlZVxcXFx4MjAnLCcxNjM3OThrUEVZZEknLCdleHByZXNzaW9uc1RvRXZhbHVhdGUnLCdzdHJpbmcnLCdjdXJyZW50Jywnbm9GdW5jdGlvbnMnLCdfYWRkUHJvcGVydHknLCdfYWRkaXRpb25hbE1ldGFkYXRhJywnZnVuY05hbWUnLCdzZW5kJywnX3BfbmFtZScsJ19leHRlbmRlZFdhcm5pbmcnLCdjYXRjaCcsJ19jb25zb2xlTmluamFBbGxvd2VkVG9TdGFydCcsJ3JlYWR5U3RhdGUnLFtcXFwibG9jYWxob3N0XFxcIixcXFwiMTI3LjAuMC4xXFxcIixcXFwiZXhhbXBsZS5jeXByZXNzLmlvXFxcIixcXFwiREVTS1RPUC1RVVFSRTlSXFxcIixcXFwiMTkyLjE2OC4xMTIuMTY5XFxcIl0sJ3BhdGhUb0ZpbGVVUkwnLCdiaWdpbnQnLCdvbm9wZW4nLCdCb29sZWFuJywnX3RyZWVOb2RlUHJvcGVydGllc0JlZm9yZUZ1bGxWYWx1ZScsJ29ubWVzc2FnZScsJ1tvYmplY3RcXFxceDIwQXJyYXldJywnQ29uc29sZVxcXFx4MjBOaW5qYVxcXFx4MjBmYWlsZWRcXFxceDIwdG9cXFxceDIwc2VuZFxcXFx4MjBsb2dzLFxcXFx4MjByZWZyZXNoaW5nXFxcXHgyMHRoZVxcXFx4MjBwYWdlXFxcXHgyMG1heVxcXFx4MjBoZWxwO1xcXFx4MjBhbHNvXFxcXHgyMHNlZVxcXFx4MjAnLCdjb25zb2xlJywnZWxlbWVudHMnLCdfZ2V0T3duUHJvcGVydHlTeW1ib2xzJywndG90YWxTdHJMZW5ndGgnLCdfaXNBcnJheScsJ01hcCcsJzc4NjEzNzBpQVNkbGcnLCcyMDc5NTg1a3dYdU1WJywnX2Nvbm5lY3RpbmcnLCdzdHJpbmdpZnknLCdfcHJvcGVydHlOYW1lJywnbG9nJywnZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yJywnUmVnRXhwJywnX3RyZWVOb2RlUHJvcGVydGllc0FmdGVyRnVsbFZhbHVlJywncmVzb2x2ZUdldHRlcnMnLCdmYWlsZWRcXFxceDIwdG9cXFxceDIwY29ubmVjdFxcXFx4MjB0b1xcXFx4MjBob3N0OlxcXFx4MjAnLCdocnRpbWUnLCdwcm9wcycsJ3VybCcsJ2xldmVsJywnY2FsbCcsJ21hcCcsJ19pc1ByaW1pdGl2ZVdyYXBwZXJUeXBlJywnX2JsYWNrbGlzdGVkUHJvcGVydHknLCdbb2JqZWN0XFxcXHgyMEJpZ0ludF0nLCdtYXRjaCcsJ1dlYlNvY2tldCcsJ3NlcmlhbGl6ZScsJ2F1dG9FeHBhbmRNYXhEZXB0aCcsJ2hvc3QnLCdzdHJMZW5ndGgnLCdtZXRob2QnLCdfaXNNYXAnLCdOdW1iZXInLCd1bnNoaWZ0JywndmVyc2lvbnMnLCdfaW5OZXh0RWRnZScsJ19TeW1ib2wnLCd1bnJlZicsJ19pc1ByaW1pdGl2ZVR5cGUnLCdsZW5ndGgnLCdfbnVtYmVyUmVnRXhwJywnX3NldE5vZGVJZCcsJ19jb25zb2xlX25pbmphJywnY29uc3RydWN0b3InLCdfY29uc29sZV9uaW5qYV9zZXNzaW9uJywncm9vdEV4cHJlc3Npb24nLFxcXCJjOlxcXFxcXFxcVXNlcnNcXFxcXFxcXGFkbWluXFxcXFxcXFwuY3Vyc29yXFxcXFxcXFxleHRlbnNpb25zXFxcXFxcXFx3YWxsYWJ5anMuY29uc29sZS1uaW5qYS0xLjAuNDYwLXVuaXZlcnNhbFxcXFxcXFxcbm9kZV9tb2R1bGVzXFxcIiwnYW5ndWxhcicsJ251bGwnLCdzbGljZScsJ1N5bWJvbCcsJ2JpbmQnLCdpbmRleCcsJ2FycmF5JywncG9wJywnYm9vbGVhbicsJ2RlZmF1bHQnLCdlZGdlJywnMTBqZ2dOSEYnLCdhc3RybycsJ19wXycsJ05FWFRfUlVOVElNRScsJ19hdHRlbXB0VG9SZWNvbm5lY3RTaG9ydGx5JywnY2FwcGVkJywnJywnMTI3LjAuMC4xJywnb2JqZWN0JywncGFyc2UnLCdfc2V0Tm9kZUV4cGFuZGFibGVTdGF0ZScsJ2VudicsJ19pbkJyb3dzZXInLCdhbGxTdHJMZW5ndGgnLCdjbG9zZScsJ3JlbWl4JywnX2lzTmVnYXRpdmVaZXJvJywnZGlzYWJsZWRMb2cnLCdfV2ViU29ja2V0Jywnb3JpZ2luJywnMjRqQkN1eG0nLCdkYXRlJywnc2VlXFxcXHgyMGh0dHBzOi8vdGlueXVybC5jb20vMnZ0OGp4endcXFxceDIwZm9yXFxcXHgyMG1vcmVcXFxceDIwaW5mby4nLCdjb3VudCcsJ3RpbWVTdGFtcCcsJ3RoZW4nLCdfYWRkT2JqZWN0UHJvcGVydHknLCdORUdBVElWRV9JTkZJTklUWScsJ19hZGRMb2FkTm9kZScsJ25hbicsJ3N5bWJvbCcsJzFsWWNRb2QnLCdvbmVycm9yJywnMTM1MzAxMjFTRWpDVkwnLCd0b1N0cmluZycsJ3dzOi8vJywnbmV4dC5qcycsJ21lc3NhZ2UnLCdsb2NhdGlvbicsJ3dzL2luZGV4LmpzJywnX3JlZ0V4cFRvU3RyaW5nJywnc2V0dGVyJywnW29iamVjdFxcXFx4MjBNYXBdJywnX2tleVN0clJlZ0V4cCcsJ2lzRXhwcmVzc2lvblRvRXZhbHVhdGUnLCclY1xcXFx4MjBDb25zb2xlXFxcXHgyME5pbmphXFxcXHgyMGV4dGVuc2lvblxcXFx4MjBpc1xcXFx4MjBjb25uZWN0ZWRcXFxceDIwdG9cXFxceDIwJywnRXJyb3InLCd0aW1lJywnX3dlYlNvY2tldEVycm9yRG9jc0xpbmsnLCdpbmNsdWRlcycsJ3Jvb3RfZXhwJywnbm9kZU1vZHVsZXMnLCdfV2ViU29ja2V0Q2xhc3MnLCdnZXQnLCdjb3ZlcmFnZScsJ0NvbnNvbGVcXFxceDIwTmluamFcXFxceDIwZmFpbGVkXFxcXHgyMHRvXFxcXHgyMHNlbmRcXFxceDIwbG9ncyxcXFxceDIwcmVzdGFydGluZ1xcXFx4MjB0aGVcXFxceDIwcHJvY2Vzc1xcXFx4MjBtYXlcXFxceDIwaGVscDtcXFxceDIwYWxzb1xcXFx4MjBzZWVcXFxceDIwJywnX3NvY2tldCcsJ3Byb3RvdHlwZScsJ3JlZHVjZUxpbWl0cycsJ19kYXRlVG9TdHJpbmcnLCdmdW5jdGlvbicsJ19pc1NldCcsJ1N0cmluZycsJ19vYmplY3RUb1N0cmluZycsJ2pvaW4nLCdfaGFzTWFwT25JdHNQYXRoJywnX3NldE5vZGVMYWJlbCcsJ3JlbG9hZCcsJ3N1YnN0cicsJ2VuZHNXaXRoJywnYXV0b0V4cGFuZExpbWl0JywnZXZlbnRSZWNlaXZlZENhbGxiYWNrJywncHJvY2VzcycsJzQyMDE5OThlaFdWeUUnLCdwZXJmb3JtYW5jZScsJ3N0YWNrVHJhY2VMaW1pdCcsJ3Rlc3QnLCdzZXQnLCdhdXRvRXhwYW5kJywnYXJncycsJ3BlcmZfaG9va3MnLCdfZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yJywnX2NhcElmU3RyaW5nJywnX2dldE93blByb3BlcnR5TmFtZXMnLCd2YWx1ZU9mJywnbm93JywnZ2V0T3duUHJvcGVydHlOYW1lcycsJy4uLicsJ19fZXMnKydNb2R1bGUnLCcyNjk3ODgwUm5rUVp2JywndHlwZScsJ3N0YWNrJywnN2VQTklQYicsJ2Zyb21DaGFyQ29kZScsJ19jb25uZWN0QXR0ZW1wdENvdW50JywnZ2xvYmFsJywnbmVnYXRpdmVaZXJvJywnX25pbmphSWdub3JlTmV4dEVycm9yJywnZXJyb3InLCdfc29ydFByb3BzJywncGF0aCcsJ2dldFdlYlNvY2tldENsYXNzJywnX2Nvbm5lY3RUb0hvc3ROb3cnLCdfc2V0Tm9kZVBlcm1pc3Npb25zJywnX3VuZGVmaW5lZCcsJ19kaXNwb3NlV2Vic29ja2V0JywnY2FwcGVkUHJvcHMnLCduZWdhdGl2ZUluZmluaXR5Jywnc29ydFByb3BzJywnSFRNTEFsbENvbGxlY3Rpb24nLCdpbmRleE9mJywnY3JlYXRlJywnX2hhc1N5bWJvbFByb3BlcnR5T25JdHNQYXRoJywnY2hhckF0JywnaGl0cycsJ25hbWUnLCdfY2xlYW5Ob2RlJywnX3NlbmRFcnJvck1lc3NhZ2UnLCdfcHJvcGVydHknLCdfdHlwZScsJzY4OTcxNktZcml5TScsJ19yZWNvbm5lY3RUaW1lb3V0Jywnbm9kZScsJ2dldHRlcicsJ3dhcm4nLCdfYWxsb3dlZFRvQ29ubmVjdE9uU2VuZCcsJ1NldCcsJ29uY2xvc2UnLCdudW1iZXInLCdwb3J0Jywncm9vdF9leHBfaWQnLCdnZXRPd25Qcm9wZXJ0eVN5bWJvbHMnLCdfYWxsb3dlZFRvU2VuZCcsJ2dhdGV3YXkuZG9ja2VyLmludGVybmFsJywnZGlzYWJsZWRUcmFjZScsJ3VuZGVmaW5lZCcsJ2h0dHBzOi8vdGlueXVybC5jb20vMzd4OGI3OXQnLCdleHBJZCcsJ2ZvckVhY2gnLCdfcHJvY2Vzc1RyZWVOb2RlUmVzdWx0JywnaGFzT3duUHJvcGVydHknLCdsb2dnZXJcXFxceDIwZmFpbGVkXFxcXHgyMHRvXFxcXHgyMGNvbm5lY3RcXFxceDIwdG9cXFxceDIwaG9zdCcsJ1BPU0lUSVZFX0lORklOSVRZJywnaXNBcnJheScsJ1tvYmplY3RcXFxceDIwRGF0ZV0nLCdfSFRNTEFsbENvbGxlY3Rpb24nLCdjb25jYXQnLCdob3N0bmFtZScsJ1xcXFx4MjBicm93c2VyJywnZG9ja2VyaXplZEFwcCddO18weDFiYWU9ZnVuY3Rpb24oKXtyZXR1cm4gXzB4NDc1NTE3O307cmV0dXJuIF8weDFiYWUoKTt9ZnVuY3Rpb24gSChfMHg1YWM1ZDMsXzB4NWEwMjRjLF8weDQxNDI4ZixfMHgyYjdjNWYsXzB4MzJiZDFjLF8weDQxMDg3MCxfMHgxNzNlYjQsXzB4NDExNTE4PW9lKXt2YXIgXzB4MzVjYTIzPV8weDI1MjFlZDtsZXQgXzB4Mjk3M2I4PV8weDQxNDI4Zlsnc3BsaXQnXSgnLCcpWydtYXAnXShfMHg0Y2UzNGQ9Pnt2YXIgXzB4NTRlODdmPV8weDIyZDQsXzB4NGJjODhjLF8weDE4OTZjMyxfMHg0YmYzZmMsXzB4MWI1ZTE0O3RyeXtpZighXzB4NWFjNWQzW18weDU0ZTg3ZigweGZhKV0pe2xldCBfMHgyZjJmN2Y9KChfMHgxODk2YzM9KF8weDRiYzg4Yz1fMHg1YWM1ZDNbXzB4NTRlODdmKDB4MTUwKV0pPT1udWxsP3ZvaWQgMHgwOl8weDRiYzg4Y1tfMHg1NGU4N2YoMHhmMCldKT09bnVsbD92b2lkIDB4MDpfMHgxODk2YzNbJ25vZGUnXSl8fCgoXzB4MWI1ZTE0PShfMHg0YmYzZmM9XzB4NWFjNWQzW18weDU0ZTg3ZigweDE1MCldKT09bnVsbD92b2lkIDB4MDpfMHg0YmYzZmNbJ2VudiddKT09bnVsbD92b2lkIDB4MDpfMHgxYjVlMTRbXzB4NTRlODdmKDB4MTBiKV0pPT09J2VkZ2UnOyhfMHgzMmJkMWM9PT1fMHg1NGU4N2YoMHhhYyl8fF8weDMyYmQxYz09PV8weDU0ZTg3ZigweDExNyl8fF8weDMyYmQxYz09PV8weDU0ZTg3ZigweDEwOSl8fF8weDMyYmQxYz09PV8weDU0ZTg3ZigweGZkKSkmJihfMHgzMmJkMWMrPV8weDJmMmY3Zj8nXFxcXHgyMHNlcnZlcic6XzB4NTRlODdmKDB4OWMpKSxfMHg1YWM1ZDNbXzB4NTRlODdmKDB4ZmEpXT17J2lkJzorbmV3IERhdGUoKSwndG9vbCc6XzB4MzJiZDFjfSxfMHgxNzNlYjQmJl8weDMyYmQxYyYmIV8weDJmMmY3ZiYmY29uc29sZVtfMHg1NGU4N2YoMHhkNyldKF8weDU0ZTg3ZigweDEzNSkrKF8weDMyYmQxY1tfMHg1NGU4N2YoMHgxNzkpXSgweDApWyd0b1VwcGVyQ2FzZSddKCkrXzB4MzJiZDFjW18weDU0ZTg3ZigweDE0YyldKDB4MSkpKycsJywnYmFja2dyb3VuZDpcXFxceDIwcmdiKDMwLDMwLDMwKTtcXFxceDIwY29sb3I6XFxcXHgyMHJnYigyNTUsMjEzLDkyKScsXzB4NTRlODdmKDB4MTFlKSk7fWxldCBfMHgzODI2NmU9bmV3IHEoXzB4NWFjNWQzLF8weDVhMDI0YyxfMHg0Y2UzNGQsXzB4MmI3YzVmLF8weDQxMDg3MCxfMHg0MTE1MTgpO3JldHVybiBfMHgzODI2NmVbXzB4NTRlODdmKDB4YmQpXVtfMHg1NGU4N2YoMHgxMDEpXShfMHgzODI2NmUpO31jYXRjaChfMHg2MDU4MTMpe3JldHVybiBjb25zb2xlW18weDU0ZTg3ZigweDE4NCldKF8weDU0ZTg3ZigweDk1KSxfMHg2MDU4MTMmJl8weDYwNTgxM1tfMHg1NGU4N2YoMHgxMmQpXSksKCk9Pnt9O319KTtyZXR1cm4gXzB4MmNiZTkyPT5fMHgyOTczYjhbXzB4MzVjYTIzKDB4OTIpXShfMHg0ZGY4MjA9Pl8weDRkZjgyMChfMHgyY2JlOTIpKTt9ZnVuY3Rpb24gb2UoXzB4NDcyM2E5LF8weGRjYWZhMyxfMHgxOGNiNWYsXzB4MzA2MjQ4KXt2YXIgXzB4Nzk3NGE2PV8weDI1MjFlZDtfMHgzMDYyNDgmJl8weDQ3MjNhOT09PV8weDc5NzRhNigweDE0YikmJl8weDE4Y2I1ZltfMHg3OTc0YTYoMHgxMmUpXVtfMHg3OTc0YTYoMHgxNGIpXSgpO31mdW5jdGlvbiBCKF8weDQ5ZGYzNil7dmFyIF8weDhjNDQ1NT1fMHgyNTIxZWQsXzB4MWFlZjYyLF8weDMzY2EwZjtsZXQgXzB4NTA0ZDRkPWZ1bmN0aW9uKF8weDNlNGQyMSxfMHgzMTE5YTkpe3JldHVybiBfMHgzMTE5YTktXzB4M2U0ZDIxO30sXzB4NGU2OGRjO2lmKF8weDQ5ZGYzNltfMHg4YzQ0NTUoMHgxNTIpXSlfMHg0ZTY4ZGM9ZnVuY3Rpb24oKXtyZXR1cm4gXzB4NDlkZjM2WydwZXJmb3JtYW5jZSddWydub3cnXSgpO307ZWxzZXtpZihfMHg0OWRmMzZbXzB4OGM0NDU1KDB4MTUwKV0mJl8weDQ5ZGYzNltfMHg4YzQ0NTUoMHgxNTApXVsnaHJ0aW1lJ10mJigoXzB4MzNjYTBmPShfMHgxYWVmNjI9XzB4NDlkZjM2Wydwcm9jZXNzJ10pPT1udWxsP3ZvaWQgMHgwOl8weDFhZWY2MltfMHg4YzQ0NTUoMHgxMTMpXSk9PW51bGw/dm9pZCAweDA6XzB4MzNjYTBmW18weDhjNDQ1NSgweDEwYildKSE9PV8weDhjNDQ1NSgweDEwNykpXzB4NGU2OGRjPWZ1bmN0aW9uKCl7dmFyIF8weDFmNTA1OD1fMHg4YzQ0NTU7cmV0dXJuIF8weDQ5ZGYzNltfMHgxZjUwNTgoMHgxNTApXVtfMHgxZjUwNTgoMHhkZCldKCk7fSxfMHg1MDRkNGQ9ZnVuY3Rpb24oXzB4NGUyMzNjLF8weDE2M2JmZil7cmV0dXJuIDB4M2U4KihfMHgxNjNiZmZbMHgwXS1fMHg0ZTIzM2NbMHgwXSkrKF8weDE2M2JmZlsweDFdLV8weDRlMjMzY1sweDFdKS8weGY0MjQwO307ZWxzZSB0cnl7bGV0IHtwZXJmb3JtYW5jZTpfMHg5MmQ2OTB9PXJlcXVpcmUoXzB4OGM0NDU1KDB4MTU4KSk7XzB4NGU2OGRjPWZ1bmN0aW9uKCl7cmV0dXJuIF8weDkyZDY5MFsnbm93J10oKTt9O31jYXRjaHtfMHg0ZTY4ZGM9ZnVuY3Rpb24oKXtyZXR1cm4rbmV3IERhdGUoKTt9O319cmV0dXJueydlbGFwc2VkJzpfMHg1MDRkNGQsJ3RpbWVTdGFtcCc6XzB4NGU2OGRjLCdub3cnOigpPT5EYXRlW18weDhjNDQ1NSgweDE1ZCldKCl9O31mdW5jdGlvbiBfMHgyMmQ0KF8weDEyZWRiNCxfMHgyNzE3ODkpe3ZhciBfMHgxYmFlMTY9XzB4MWJhZSgpO3JldHVybiBfMHgyMmQ0PWZ1bmN0aW9uKF8weDIyZDRkZSxfMHg1ZDQzNWQpe18weDIyZDRkZT1fMHgyMmQ0ZGUtMHg4Zjt2YXIgXzB4NWNmMzk5PV8weDFiYWUxNltfMHgyMmQ0ZGVdO3JldHVybiBfMHg1Y2YzOTk7fSxfMHgyMmQ0KF8weDEyZWRiNCxfMHgyNzE3ODkpO31mdW5jdGlvbiBYKF8weDM1M2IxNSxfMHgxYWE3MzAsXzB4MWRlMzZlKXt2YXIgXzB4MjkwZmQ9XzB4MjUyMWVkLF8weDJjZDRhZSxfMHg0MjQ2MTksXzB4NGYyYTZjLF8weDFkOTk4NixfMHgyMzAwNTY7aWYoXzB4MzUzYjE1W18weDI5MGZkKDB4YzEpXSE9PXZvaWQgMHgwKXJldHVybiBfMHgzNTNiMTVbXzB4MjkwZmQoMHhjMSldO2xldCBfMHg1NDJhMDI9KChfMHg0MjQ2MTk9KF8weDJjZDRhZT1fMHgzNTNiMTVbJ3Byb2Nlc3MnXSk9PW51bGw/dm9pZCAweDA6XzB4MmNkNGFlW18weDI5MGZkKDB4ZjApXSk9PW51bGw/dm9pZCAweDA6XzB4NDI0NjE5W18weDI5MGZkKDB4MTgyKV0pfHwoKF8weDFkOTk4Nj0oXzB4NGYyYTZjPV8weDM1M2IxNVtfMHgyOTBmZCgweDE1MCldKT09bnVsbD92b2lkIDB4MDpfMHg0ZjJhNmNbXzB4MjkwZmQoMHgxMTMpXSk9PW51bGw/dm9pZCAweDA6XzB4MWQ5OTg2W18weDI5MGZkKDB4MTBiKV0pPT09J2VkZ2UnO2Z1bmN0aW9uIF8weDM5NGEyYihfMHg0NWJkMzUpe3ZhciBfMHg1ZWI3NDk9XzB4MjkwZmQ7aWYoXzB4NDViZDM1WydzdGFydHNXaXRoJ10oJy8nKSYmXzB4NDViZDM1W18weDVlYjc0OSgweDE0ZCldKCcvJykpe2xldCBfMHgyMWExMGY9bmV3IFJlZ0V4cChfMHg0NWJkMzVbXzB4NWViNzQ5KDB4ZmYpXSgweDEsLTB4MSkpO3JldHVybiBfMHhlMGZjYTU9Pl8weDIxYTEwZltfMHg1ZWI3NDkoMHgxNTQpXShfMHhlMGZjYTUpO31lbHNle2lmKF8weDQ1YmQzNVtfMHg1ZWI3NDkoMHgxMzkpXSgnKicpfHxfMHg0NWJkMzVbXzB4NWViNzQ5KDB4MTM5KV0oJz8nKSl7bGV0IF8weDk1YzgxMT1uZXcgUmVnRXhwKCdeJytfMHg0NWJkMzVbJ3JlcGxhY2UnXSgvXFxcXC4vZyxTdHJpbmdbXzB4NWViNzQ5KDB4MTY1KV0oMHg1YykrJy4nKVsncmVwbGFjZSddKC9cXFxcKi9nLCcuKicpWydyZXBsYWNlJ10oL1xcXFw/L2csJy4nKStTdHJpbmdbXzB4NWViNzQ5KDB4MTY1KV0oMHgyNCkpO3JldHVybiBfMHg0MGVjOTQ9Pl8weDk1YzgxMVtfMHg1ZWI3NDkoMHgxNTQpXShfMHg0MGVjOTQpO31lbHNlIHJldHVybiBfMHgzZjI0ZGQ9Pl8weDNmMjRkZD09PV8weDQ1YmQzNTt9fWxldCBfMHgyY2E3ZGY9XzB4MWFhNzMwW18weDI5MGZkKDB4ZTIpXShfMHgzOTRhMmIpO3JldHVybiBfMHgzNTNiMTVbXzB4MjkwZmQoMHhjMSldPV8weDU0MmEwMnx8IV8weDFhYTczMCwhXzB4MzUzYjE1W18weDI5MGZkKDB4YzEpXSYmKChfMHgyMzAwNTY9XzB4MzUzYjE1W18weDI5MGZkKDB4MTJlKV0pPT1udWxsP3ZvaWQgMHgwOl8weDIzMDA1NlsnaG9zdG5hbWUnXSkmJihfMHgzNTNiMTVbXzB4MjkwZmQoMHhjMSldPV8weDJjYTdkZlsnc29tZSddKF8weDIzZmUyYj0+XzB4MjNmZTJiKF8weDM1M2IxNVtfMHgyOTBmZCgweDEyZSldW18weDI5MGZkKDB4OWIpXSkpKSxfMHgzNTNiMTVbJ19jb25zb2xlTmluamFBbGxvd2VkVG9TdGFydCddO31mdW5jdGlvbiBKKF8weDFjNmViMCxfMHg1NTI5MTksXzB4NTFkZTE4LF8weDUzM2M5Yyl7dmFyIF8weGQ1YTU5ZD1fMHgyNTIxZWQ7XzB4MWM2ZWIwPV8weDFjNmViMCxfMHg1NTI5MTk9XzB4NTUyOTE5LF8weDUxZGUxOD1fMHg1MWRlMTgsXzB4NTMzYzljPV8weDUzM2M5YztsZXQgXzB4MTEzNmVmPUIoXzB4MWM2ZWIwKSxfMHgxYTFiODA9XzB4MTEzNmVmWydlbGFwc2VkJ10sXzB4NTdkOTQ1PV8weDExMzZlZltfMHhkNWE1OWQoMHgxMjApXTtjbGFzcyBfMHg0YTIxMzd7Y29uc3RydWN0b3IoKXt2YXIgXzB4MjBhODcyPV8weGQ1YTU5ZDt0aGlzW18weDIwYTg3MigweDEzMyldPS9eKD8hKD86ZG98aWZ8aW58Zm9yfGxldHxuZXd8dHJ5fHZhcnxjYXNlfGVsc2V8ZW51bXxldmFsfGZhbHNlfG51bGx8dGhpc3x0cnVlfHZvaWR8d2l0aHxicmVha3xjYXRjaHxjbGFzc3xjb25zdHxzdXBlcnx0aHJvd3x3aGlsZXx5aWVsZHxkZWxldGV8ZXhwb3J0fGltcG9ydHxwdWJsaWN8cmV0dXJufHN0YXRpY3xzd2l0Y2h8dHlwZW9mfGRlZmF1bHR8ZXh0ZW5kc3xmaW5hbGx5fHBhY2thZ2V8cHJpdmF0ZXxjb250aW51ZXxkZWJ1Z2dlcnxmdW5jdGlvbnxhcmd1bWVudHN8aW50ZXJmYWNlfHByb3RlY3RlZHxpbXBsZW1lbnRzfGluc3RhbmNlb2YpJClbXyRhLXpBLVpcXFxceEEwLVxcXFx1RkZGRl1bXyRhLXpBLVowLTlcXFxceEEwLVxcXFx1RkZGRl0qJC8sdGhpc1tfMHgyMGE4NzIoMHhmNildPS9eKDB8WzEtOV1bMC05XSopJC8sdGhpc1snX3F1b3RlZFJlZ0V4cCddPS8nKFteXFxcXFxcXFwnXXxcXFxcXFxcXCcpKicvLHRoaXNbXzB4MjBhODcyKDB4MTcwKV09XzB4MWM2ZWIwW18weDIwYTg3MigweDhmKV0sdGhpc1tfMHgyMGE4NzIoMHg5OSldPV8weDFjNmViMFsnSFRNTEFsbENvbGxlY3Rpb24nXSx0aGlzW18weDIwYTg3MigweDE1OSldPU9iamVjdFtfMHgyMGE4NzIoMHhkOCldLHRoaXNbXzB4MjBhODcyKDB4MTViKV09T2JqZWN0W18weDIwYTg3MigweDE1ZSldLHRoaXNbXzB4MjBhODcyKDB4ZjIpXT1fMHgxYzZlYjBbXzB4MjBhODcyKDB4MTAwKV0sdGhpc1tfMHgyMGE4NzIoMHgxMzApXT1SZWdFeHBbXzB4MjBhODcyKDB4MTQxKV1bXzB4MjBhODcyKDB4MTJhKV0sdGhpc1snX2RhdGVUb1N0cmluZyddPURhdGVbXzB4MjBhODcyKDB4MTQxKV1bXzB4MjBhODcyKDB4MTJhKV07fVtfMHhkNWE1OWQoMHhlOCldKF8weDE5ZTdkMixfMHhkZWZmOTcsXzB4MWI0MWUwLF8weDVhOGQxOCl7dmFyIF8weDM2OWE0MD1fMHhkNWE1OWQsXzB4NGUwYTBiPXRoaXMsXzB4MjA3NDE4PV8weDFiNDFlMFtfMHgzNjlhNDAoMHgxNTYpXTtmdW5jdGlvbiBfMHgzNTZhYjcoXzB4MTc2NGEwLF8weDI4Y2I2OSxfMHg1Y2QwODYpe3ZhciBfMHgyZTg1ZTQ9XzB4MzY5YTQwO18weDI4Y2I2OVtfMHgyZTg1ZTQoMHgxNjIpXT1fMHgyZTg1ZTQoMHg5ZSksXzB4MjhjYjY5W18weDJlODVlNCgweDE2YSldPV8weDE3NjRhMFtfMHgyZTg1ZTQoMHgxMmQpXSxfMHgxZjkwNzM9XzB4NWNkMDg2W18weDJlODVlNCgweDE4MildW18weDJlODVlNCgweGI4KV0sXzB4NWNkMDg2W18weDJlODVlNCgweDE4MildW18weDJlODVlNCgweGI4KV09XzB4MjhjYjY5LF8weDRlMGEwYltfMHgyZTg1ZTQoMHhjOCldKF8weDI4Y2I2OSxfMHg1Y2QwODYpO31sZXQgXzB4MzcxYWU1O18weDFjNmViMFtfMHgzNjlhNDAoMHhjYyldJiYoXzB4MzcxYWU1PV8weDFjNmViMFtfMHgzNjlhNDAoMHhjYyldW18weDM2OWE0MCgweDE2YSldLF8weDM3MWFlNSYmKF8weDFjNmViMFsnY29uc29sZSddW18weDM2OWE0MCgweDE2YSldPWZ1bmN0aW9uKCl7fSkpO3RyeXt0cnl7XzB4MWI0MWUwWydsZXZlbCddKyssXzB4MWI0MWUwW18weDM2OWE0MCgweDE1NildJiZfMHgxYjQxZTBbJ2F1dG9FeHBhbmRQcmV2aW91c09iamVjdHMnXVtfMHgzNjlhNDAoMHhiMSldKF8weGRlZmY5Nyk7dmFyIF8weDFiMmNmOCxfMHg1NjM0YTgsXzB4MjRhY2EzLF8weDUyNDg5MyxfMHgyYTc4NTM9W10sXzB4MzcyNWY5PVtdLF8weDFiOTVkNixfMHg1ZjM4NTc9dGhpc1tfMHgzNjlhNDAoMHgxN2YpXShfMHhkZWZmOTcpLF8weDQwZGM4OT1fMHg1ZjM4NTc9PT1fMHgzNjlhNDAoMHgxMDMpLF8weDNhMGY4MT0hMHgxLF8weDNlNjYwNj1fMHg1ZjM4NTc9PT1fMHgzNjlhNDAoMHgxNDQpLF8weDEyYzgwYj10aGlzW18weDM2OWE0MCgweGY0KV0oXzB4NWYzODU3KSxfMHg0MDA4MDQ9dGhpc1tfMHgzNjlhNDAoMHhlMyldKF8weDVmMzg1NyksXzB4NGE5Nzc2PV8weDEyYzgwYnx8XzB4NDAwODA0LF8weDUzMzVlYT17fSxfMHgzNTAyZTc9MHgwLF8weDI4ODVlMz0hMHgxLF8weDFmOTA3MyxfMHgzMThlMTg9L14oKFsxLTldezF9WzAtOV0qKXwwKSQvO2lmKF8weDFiNDFlMFtfMHgzNjlhNDAoMHhhZildKXtpZihfMHg0MGRjODkpe2lmKF8weDU2MzRhOD1fMHhkZWZmOTdbXzB4MzY5YTQwKDB4ZjUpXSxfMHg1NjM0YTg+XzB4MWI0MWUwWydlbGVtZW50cyddKXtmb3IoXzB4MjRhY2EzPTB4MCxfMHg1MjQ4OTM9XzB4MWI0MWUwWydlbGVtZW50cyddLF8weDFiMmNmOD1fMHgyNGFjYTM7XzB4MWIyY2Y4PF8weDUyNDg5MztfMHgxYjJjZjgrKylfMHgzNzI1ZjlbJ3B1c2gnXShfMHg0ZTBhMGJbXzB4MzY5YTQwKDB4YmEpXShfMHgyYTc4NTMsXzB4ZGVmZjk3LF8weDVmMzg1NyxfMHgxYjJjZjgsXzB4MWI0MWUwKSk7XzB4MTllN2QyWydjYXBwZWRFbGVtZW50cyddPSEweDA7fWVsc2V7Zm9yKF8weDI0YWNhMz0weDAsXzB4NTI0ODkzPV8weDU2MzRhOCxfMHgxYjJjZjg9XzB4MjRhY2EzO18weDFiMmNmODxfMHg1MjQ4OTM7XzB4MWIyY2Y4KyspXzB4MzcyNWY5W18weDM2OWE0MCgweGIxKV0oXzB4NGUwYTBiW18weDM2OWE0MCgweGJhKV0oXzB4MmE3ODUzLF8weGRlZmY5NyxfMHg1ZjM4NTcsXzB4MWIyY2Y4LF8weDFiNDFlMCkpO31fMHgxYjQxZTBbJ2F1dG9FeHBhbmRQcm9wZXJ0eUNvdW50J10rPV8weDM3MjVmOVtfMHgzNjlhNDAoMHhmNSldO31pZighKF8weDVmMzg1Nz09PSdudWxsJ3x8XzB4NWYzODU3PT09XzB4MzY5YTQwKDB4OGYpKSYmIV8weDEyYzgwYiYmXzB4NWYzODU3IT09XzB4MzY5YTQwKDB4MTQ2KSYmXzB4NWYzODU3IT09J0J1ZmZlcicmJl8weDVmMzg1NyE9PSdiaWdpbnQnKXt2YXIgXzB4OWIxNjRkPV8weDVhOGQxOFtfMHgzNjlhNDAoMHhkZSldfHxfMHgxYjQxZTBbXzB4MzY5YTQwKDB4ZGUpXTtpZih0aGlzWydfaXNTZXQnXShfMHhkZWZmOTcpPyhfMHgxYjJjZjg9MHgwLF8weGRlZmY5N1tfMHgzNjlhNDAoMHg5MildKGZ1bmN0aW9uKF8weDJlNGM4Myl7dmFyIF8weGMzNGNmNT1fMHgzNjlhNDA7aWYoXzB4MzUwMmU3KyssXzB4MWI0MWUwWydhdXRvRXhwYW5kUHJvcGVydHlDb3VudCddKyssXzB4MzUwMmU3Pl8weDliMTY0ZCl7XzB4Mjg4NWUzPSEweDA7cmV0dXJuO31pZighXzB4MWI0MWUwWydpc0V4cHJlc3Npb25Ub0V2YWx1YXRlJ10mJl8weDFiNDFlMFtfMHhjMzRjZjUoMHgxNTYpXSYmXzB4MWI0MWUwW18weGMzNGNmNSgweGE5KV0+XzB4MWI0MWUwW18weGMzNGNmNSgweDE0ZSldKXtfMHgyODg1ZTM9ITB4MDtyZXR1cm47fV8weDM3MjVmOVtfMHhjMzRjZjUoMHhiMSldKF8weDRlMGEwYltfMHhjMzRjZjUoMHhiYSldKF8weDJhNzg1MyxfMHhkZWZmOTcsXzB4YzM0Y2Y1KDB4MTg2KSxfMHgxYjJjZjgrKyxfMHgxYjQxZTAsZnVuY3Rpb24oXzB4MjlkNzk2KXtyZXR1cm4gZnVuY3Rpb24oKXtyZXR1cm4gXzB4MjlkNzk2O307fShfMHgyZTRjODMpKSk7fSkpOnRoaXNbXzB4MzY5YTQwKDB4ZWQpXShfMHhkZWZmOTcpJiZfMHhkZWZmOTdbXzB4MzY5YTQwKDB4OTIpXShmdW5jdGlvbihfMHhmZWEwZmUsXzB4YTE0ODAzKXt2YXIgXzB4MzJhYTcyPV8weDM2OWE0MDtpZihfMHgzNTAyZTcrKyxfMHgxYjQxZTBbXzB4MzJhYTcyKDB4YTkpXSsrLF8weDM1MDJlNz5fMHg5YjE2NGQpe18weDI4ODVlMz0hMHgwO3JldHVybjt9aWYoIV8weDFiNDFlMFsnaXNFeHByZXNzaW9uVG9FdmFsdWF0ZSddJiZfMHgxYjQxZTBbXzB4MzJhYTcyKDB4MTU2KV0mJl8weDFiNDFlMFsnYXV0b0V4cGFuZFByb3BlcnR5Q291bnQnXT5fMHgxYjQxZTBbJ2F1dG9FeHBhbmRMaW1pdCddKXtfMHgyODg1ZTM9ITB4MDtyZXR1cm47fXZhciBfMHgzZTg3MWQ9XzB4YTE0ODAzWyd0b1N0cmluZyddKCk7XzB4M2U4NzFkWydsZW5ndGgnXT4weDY0JiYoXzB4M2U4NzFkPV8weDNlODcxZFtfMHgzMmFhNzIoMHhmZildKDB4MCwweDY0KStfMHgzMmFhNzIoMHgxNWYpKSxfMHgzNzI1ZjlbXzB4MzJhYTcyKDB4YjEpXShfMHg0ZTBhMGJbXzB4MzJhYTcyKDB4YmEpXShfMHgyYTc4NTMsXzB4ZGVmZjk3LF8weDMyYWE3MigweGQxKSxfMHgzZTg3MWQsXzB4MWI0MWUwLGZ1bmN0aW9uKF8weDRiOTM3Nyl7cmV0dXJuIGZ1bmN0aW9uKCl7cmV0dXJuIF8weDRiOTM3Nzt9O30oXzB4ZmVhMGZlKSkpO30pLCFfMHgzYTBmODEpe3RyeXtmb3IoXzB4MWI5NWQ2IGluIF8weGRlZmY5NylpZighKF8weDQwZGM4OSYmXzB4MzE4ZTE4W18weDM2OWE0MCgweDE1NCldKF8weDFiOTVkNikpJiYhdGhpc1tfMHgzNjlhNDAoMHhlNCldKF8weGRlZmY5NyxfMHgxYjk1ZDYsXzB4MWI0MWUwKSl7aWYoXzB4MzUwMmU3KyssXzB4MWI0MWUwW18weDM2OWE0MCgweGE5KV0rKyxfMHgzNTAyZTc+XzB4OWIxNjRkKXtfMHgyODg1ZTM9ITB4MDticmVhazt9aWYoIV8weDFiNDFlMFtfMHgzNjlhNDAoMHgxMzQpXSYmXzB4MWI0MWUwW18weDM2OWE0MCgweDE1NildJiZfMHgxYjQxZTBbXzB4MzY5YTQwKDB4YTkpXT5fMHgxYjQxZTBbJ2F1dG9FeHBhbmRMaW1pdCddKXtfMHgyODg1ZTM9ITB4MDticmVhazt9XzB4MzcyNWY5W18weDM2OWE0MCgweGIxKV0oXzB4NGUwYTBiWydfYWRkT2JqZWN0UHJvcGVydHknXShfMHgyYTc4NTMsXzB4NTMzNWVhLF8weGRlZmY5NyxfMHg1ZjM4NTcsXzB4MWI5NWQ2LF8weDFiNDFlMCkpO319Y2F0Y2h7fWlmKF8weDUzMzVlYVtfMHgzNjlhNDAoMHhhZSldPSEweDAsXzB4M2U2NjA2JiYoXzB4NTMzNWVhW18weDM2OWE0MCgweGJlKV09ITB4MCksIV8weDI4ODVlMyl7dmFyIF8weDNiNmIzYT1bXVtfMHgzNjlhNDAoMHg5YSldKHRoaXNbXzB4MzY5YTQwKDB4MTViKV0oXzB4ZGVmZjk3KSlbJ2NvbmNhdCddKHRoaXNbXzB4MzY5YTQwKDB4Y2UpXShfMHhkZWZmOTcpKTtmb3IoXzB4MWIyY2Y4PTB4MCxfMHg1NjM0YTg9XzB4M2I2YjNhW18weDM2OWE0MCgweGY1KV07XzB4MWIyY2Y4PF8weDU2MzRhODtfMHgxYjJjZjgrKylpZihfMHgxYjk1ZDY9XzB4M2I2YjNhW18weDFiMmNmOF0sIShfMHg0MGRjODkmJl8weDMxOGUxOFtfMHgzNjlhNDAoMHgxNTQpXShfMHgxYjk1ZDZbJ3RvU3RyaW5nJ10oKSkpJiYhdGhpc1tfMHgzNjlhNDAoMHhlNCldKF8weGRlZmY5NyxfMHgxYjk1ZDYsXzB4MWI0MWUwKSYmIV8weDUzMzVlYVtfMHgzNjlhNDAoMHgxMGEpK18weDFiOTVkNltfMHgzNjlhNDAoMHgxMmEpXSgpXSl7aWYoXzB4MzUwMmU3KyssXzB4MWI0MWUwW18weDM2OWE0MCgweGE5KV0rKyxfMHgzNTAyZTc+XzB4OWIxNjRkKXtfMHgyODg1ZTM9ITB4MDticmVhazt9aWYoIV8weDFiNDFlMFtfMHgzNjlhNDAoMHgxMzQpXSYmXzB4MWI0MWUwW18weDM2OWE0MCgweDE1NildJiZfMHgxYjQxZTBbXzB4MzY5YTQwKDB4YTkpXT5fMHgxYjQxZTBbXzB4MzY5YTQwKDB4MTRlKV0pe18weDI4ODVlMz0hMHgwO2JyZWFrO31fMHgzNzI1ZjlbXzB4MzY5YTQwKDB4YjEpXShfMHg0ZTBhMGJbXzB4MzY5YTQwKDB4MTIyKV0oXzB4MmE3ODUzLF8weDUzMzVlYSxfMHhkZWZmOTcsXzB4NWYzODU3LF8weDFiOTVkNixfMHgxYjQxZTApKTt9fX19fWlmKF8weDE5ZTdkMlsndHlwZSddPV8weDVmMzg1NyxfMHg0YTk3NzY/KF8weDE5ZTdkMlsndmFsdWUnXT1fMHhkZWZmOTdbXzB4MzY5YTQwKDB4MTVjKV0oKSx0aGlzW18weDM2OWE0MCgweDE1YSldKF8weDVmMzg1NyxfMHgxOWU3ZDIsXzB4MWI0MWUwLF8weDVhOGQxOCkpOl8weDVmMzg1Nz09PV8weDM2OWE0MCgweDExZCk/XzB4MTllN2QyW18weDM2OWE0MCgweGE3KV09dGhpc1tfMHgzNjlhNDAoMHgxNDMpXVtfMHgzNjlhNDAoMHhlMSldKF8weGRlZmY5Nyk6XzB4NWYzODU3PT09XzB4MzY5YTQwKDB4YzUpP18weDE5ZTdkMltfMHgzNjlhNDAoMHhhNyldPV8weGRlZmY5N1tfMHgzNjlhNDAoMHgxMmEpXSgpOl8weDVmMzg1Nz09PV8weDM2OWE0MCgweGQ5KT9fMHgxOWU3ZDJbXzB4MzY5YTQwKDB4YTcpXT10aGlzW18weDM2OWE0MCgweDEzMCldWydjYWxsJ10oXzB4ZGVmZjk3KTpfMHg1ZjM4NTc9PT0nc3ltYm9sJyYmdGhpc1snX1N5bWJvbCddP18weDE5ZTdkMlsndmFsdWUnXT10aGlzW18weDM2OWE0MCgweGYyKV1bXzB4MzY5YTQwKDB4MTQxKV1bXzB4MzY5YTQwKDB4MTJhKV1bXzB4MzY5YTQwKDB4ZTEpXShfMHhkZWZmOTcpOiFfMHgxYjQxZTBbXzB4MzY5YTQwKDB4YWYpXSYmIShfMHg1ZjM4NTc9PT1fMHgzNjlhNDAoMHhmZSl8fF8weDVmMzg1Nz09PSd1bmRlZmluZWQnKSYmKGRlbGV0ZSBfMHgxOWU3ZDJbXzB4MzY5YTQwKDB4YTcpXSxfMHgxOWU3ZDJbJ2NhcHBlZCddPSEweDApLF8weDI4ODVlMyYmKF8weDE5ZTdkMltfMHgzNjlhNDAoMHgxNzIpXT0hMHgwKSxfMHgxZjkwNzM9XzB4MWI0MWUwW18weDM2OWE0MCgweDE4MildW18weDM2OWE0MCgweGI4KV0sXzB4MWI0MWUwW18weDM2OWE0MCgweDE4MildW18weDM2OWE0MCgweGI4KV09XzB4MTllN2QyLHRoaXNbJ190cmVlTm9kZVByb3BlcnRpZXNCZWZvcmVGdWxsVmFsdWUnXShfMHgxOWU3ZDIsXzB4MWI0MWUwKSxfMHgzNzI1ZjlbJ2xlbmd0aCddKXtmb3IoXzB4MWIyY2Y4PTB4MCxfMHg1NjM0YTg9XzB4MzcyNWY5WydsZW5ndGgnXTtfMHgxYjJjZjg8XzB4NTYzNGE4O18weDFiMmNmOCsrKV8weDM3MjVmOVtfMHgxYjJjZjhdKF8weDFiMmNmOCk7fV8weDJhNzg1M1snbGVuZ3RoJ10mJihfMHgxOWU3ZDJbJ3Byb3BzJ109XzB4MmE3ODUzKTt9Y2F0Y2goXzB4NDMyNTVjKXtfMHgzNTZhYjcoXzB4NDMyNTVjLF8weDE5ZTdkMixfMHgxYjQxZTApO310aGlzW18weDM2OWE0MCgweGJiKV0oXzB4ZGVmZjk3LF8weDE5ZTdkMiksdGhpc1snX3RyZWVOb2RlUHJvcGVydGllc0FmdGVyRnVsbFZhbHVlJ10oXzB4MTllN2QyLF8weDFiNDFlMCksXzB4MWI0MWUwW18weDM2OWE0MCgweDE4MildW18weDM2OWE0MCgweGI4KV09XzB4MWY5MDczLF8weDFiNDFlMFtfMHgzNjlhNDAoMHhlMCldLS0sXzB4MWI0MWUwW18weDM2OWE0MCgweDE1NildPV8weDIwNzQxOCxfMHgxYjQxZTBbXzB4MzY5YTQwKDB4MTU2KV0mJl8weDFiNDFlMFsnYXV0b0V4cGFuZFByZXZpb3VzT2JqZWN0cyddW18weDM2OWE0MCgweDEwNCldKCk7fWZpbmFsbHl7XzB4MzcxYWU1JiYoXzB4MWM2ZWIwWydjb25zb2xlJ11bXzB4MzY5YTQwKDB4MTZhKV09XzB4MzcxYWU1KTt9cmV0dXJuIF8weDE5ZTdkMjt9WydfZ2V0T3duUHJvcGVydHlTeW1ib2xzJ10oXzB4NDc1NzFkKXt2YXIgXzB4MWI3OWQ0PV8weGQ1YTU5ZDtyZXR1cm4gT2JqZWN0W18weDFiNzlkNCgweDE4YildP09iamVjdFtfMHgxYjc5ZDQoMHgxOGIpXShfMHg0NzU3MWQpOltdO31bXzB4ZDVhNTlkKDB4MTQ1KV0oXzB4MjM2NWExKXt2YXIgXzB4OTVjOTdiPV8weGQ1YTU5ZDtyZXR1cm4hIShfMHgyMzY1YTEmJl8weDFjNmViMFtfMHg5NWM5N2IoMHgxODYpXSYmdGhpc1tfMHg5NWM5N2IoMHgxNDcpXShfMHgyMzY1YTEpPT09XzB4OTVjOTdiKDB4YTQpJiZfMHgyMzY1YTFbJ2ZvckVhY2gnXSk7fVtfMHhkNWE1OWQoMHhlNCldKF8weDEzZjg5YyxfMHgxNTRiZWYsXzB4NDNhMmFmKXtyZXR1cm4gXzB4NDNhMmFmWydub0Z1bmN0aW9ucyddP3R5cGVvZiBfMHgxM2Y4OWNbXzB4MTU0YmVmXT09J2Z1bmN0aW9uJzohMHgxO31bXzB4ZDVhNTlkKDB4MTdmKV0oXzB4NDBkODg2KXt2YXIgXzB4MmI1OTcxPV8weGQ1YTU5ZCxfMHg1MDhmZjA9Jyc7cmV0dXJuIF8weDUwOGZmMD10eXBlb2YgXzB4NDBkODg2LF8weDUwOGZmMD09PV8weDJiNTk3MSgweDExMCk/dGhpc1tfMHgyYjU5NzEoMHgxNDcpXShfMHg0MGQ4ODYpPT09J1tvYmplY3RcXFxceDIwQXJyYXldJz9fMHg1MDhmZjA9XzB4MmI1OTcxKDB4MTAzKTp0aGlzW18weDJiNTk3MSgweDE0NyldKF8weDQwZDg4Nik9PT1fMHgyYjU5NzEoMHg5OCk/XzB4NTA4ZmYwPV8weDJiNTk3MSgweDExZCk6dGhpc1snX29iamVjdFRvU3RyaW5nJ10oXzB4NDBkODg2KT09PV8weDJiNTk3MSgweGU1KT9fMHg1MDhmZjA9J2JpZ2ludCc6XzB4NDBkODg2PT09bnVsbD9fMHg1MDhmZjA9XzB4MmI1OTcxKDB4ZmUpOl8weDQwZDg4NltfMHgyYjU5NzEoMHhmOSldJiYoXzB4NTA4ZmYwPV8weDQwZDg4NltfMHgyYjU5NzEoMHhmOSldWyduYW1lJ118fF8weDUwOGZmMCk6XzB4NTA4ZmYwPT09XzB4MmI1OTcxKDB4OGYpJiZ0aGlzWydfSFRNTEFsbENvbGxlY3Rpb24nXSYmXzB4NDBkODg2IGluc3RhbmNlb2YgdGhpc1tfMHgyYjU5NzEoMHg5OSldJiYoXzB4NTA4ZmYwPV8weDJiNTk3MSgweDE3NSkpLF8weDUwOGZmMDt9Wydfb2JqZWN0VG9TdHJpbmcnXShfMHg1ODk4NDgpe3ZhciBfMHgyNzQyZmE9XzB4ZDVhNTlkO3JldHVybiBPYmplY3RbJ3Byb3RvdHlwZSddWyd0b1N0cmluZyddW18weDI3NDJmYSgweGUxKV0oXzB4NTg5ODQ4KTt9W18weGQ1YTU5ZCgweGY0KV0oXzB4NTJmNDIxKXt2YXIgXzB4MjhmODJmPV8weGQ1YTU5ZDtyZXR1cm4gXzB4NTJmNDIxPT09XzB4MjhmODJmKDB4MTA1KXx8XzB4NTJmNDIxPT09XzB4MjhmODJmKDB4YjcpfHxfMHg1MmY0MjE9PT1fMHgyOGY4MmYoMHgxODgpO31bXzB4ZDVhNTlkKDB4ZTMpXShfMHgzNTViYmQpe3ZhciBfMHg1MTk2MmY9XzB4ZDVhNTlkO3JldHVybiBfMHgzNTViYmQ9PT1fMHg1MTk2MmYoMHhjNyl8fF8weDM1NWJiZD09PV8weDUxOTYyZigweDE0Nil8fF8weDM1NWJiZD09PV8weDUxOTYyZigweGVlKTt9W18weGQ1YTU5ZCgweGJhKV0oXzB4OWJhOThjLF8weDU5NmIzOSxfMHgzZTI0NTIsXzB4OTIzY2YwLF8weDFmOGU3YSxfMHgzY2NiZWQpe3ZhciBfMHg0ZTljNDg9dGhpcztyZXR1cm4gZnVuY3Rpb24oXzB4MWU4NGMzKXt2YXIgXzB4MjVlMzMxPV8weDIyZDQsXzB4MTExOWZlPV8weDFmOGU3YVsnbm9kZSddW18weDI1ZTMzMSgweGI4KV0sXzB4MzA2MWZjPV8weDFmOGU3YVtfMHgyNWUzMzEoMHgxODIpXVsnaW5kZXgnXSxfMHg0MjIxMDA9XzB4MWY4ZTdhW18weDI1ZTMzMSgweDE4MildW18weDI1ZTMzMSgweGEyKV07XzB4MWY4ZTdhW18weDI1ZTMzMSgweDE4MildW18weDI1ZTMzMSgweGEyKV09XzB4MTExOWZlLF8weDFmOGU3YVtfMHgyNWUzMzEoMHgxODIpXVsnaW5kZXgnXT10eXBlb2YgXzB4OTIzY2YwPT1fMHgyNWUzMzEoMHgxODgpP18weDkyM2NmMDpfMHgxZTg0YzMsXzB4OWJhOThjWydwdXNoJ10oXzB4NGU5YzQ4W18weDI1ZTMzMSgweDE3ZSldKF8weDU5NmIzOSxfMHgzZTI0NTIsXzB4OTIzY2YwLF8weDFmOGU3YSxfMHgzY2NiZWQpKSxfMHgxZjhlN2FbJ25vZGUnXVsncGFyZW50J109XzB4NDIyMTAwLF8weDFmOGU3YVtfMHgyNWUzMzEoMHgxODIpXVtfMHgyNWUzMzEoMHgxMDIpXT1fMHgzMDYxZmM7fTt9WydfYWRkT2JqZWN0UHJvcGVydHknXShfMHhmN2M1NzgsXzB4NTE5NzFkLF8weDMzYzQ0ZSxfMHg1MjhhMTQsXzB4Mjk3MzA5LF8weDRlYWQ0NixfMHg1MWRlMjgpe3ZhciBfMHgyNDBkNTU9XzB4ZDVhNTlkLF8weDNkOGEyMj10aGlzO3JldHVybiBfMHg1MTk3MWRbJ19wXycrXzB4Mjk3MzA5W18weDI0MGQ1NSgweDEyYSldKCldPSEweDAsZnVuY3Rpb24oXzB4NTIzNDk1KXt2YXIgXzB4MjQyNGJmPV8weDI0MGQ1NSxfMHg1ZDE5MzA9XzB4NGVhZDQ2W18weDI0MjRiZigweDE4MildW18weDI0MjRiZigweGI4KV0sXzB4MzZiNmY5PV8weDRlYWQ0NltfMHgyNDI0YmYoMHgxODIpXVtfMHgyNDI0YmYoMHgxMDIpXSxfMHgyNDIyMTc9XzB4NGVhZDQ2Wydub2RlJ11bJ3BhcmVudCddO18weDRlYWQ0Nlsnbm9kZSddW18weDI0MjRiZigweGEyKV09XzB4NWQxOTMwLF8weDRlYWQ0NltfMHgyNDI0YmYoMHgxODIpXVtfMHgyNDI0YmYoMHgxMDIpXT1fMHg1MjM0OTUsXzB4ZjdjNTc4WydwdXNoJ10oXzB4M2Q4YTIyWydfcHJvcGVydHknXShfMHgzM2M0NGUsXzB4NTI4YTE0LF8weDI5NzMwOSxfMHg0ZWFkNDYsXzB4NTFkZTI4KSksXzB4NGVhZDQ2W18weDI0MjRiZigweDE4MildW18weDI0MjRiZigweGEyKV09XzB4MjQyMjE3LF8weDRlYWQ0NltfMHgyNDI0YmYoMHgxODIpXVtfMHgyNDI0YmYoMHgxMDIpXT1fMHgzNmI2Zjk7fTt9W18weGQ1YTU5ZCgweDE3ZSldKF8weDJmY2IwYixfMHg1MGRmMzIsXzB4NTM4NDc4LF8weDU0OGQ5OSxfMHhiOWUwMjkpe3ZhciBfMHgzMDA2ZGQ9XzB4ZDVhNTlkLF8weDIwZjBkOT10aGlzO18weGI5ZTAyOXx8KF8weGI5ZTAyOT1mdW5jdGlvbihfMHgxMTMwYjcsXzB4NWFlNzUxKXtyZXR1cm4gXzB4MTEzMGI3W18weDVhZTc1MV07fSk7dmFyIF8weDUzMDYzMz1fMHg1Mzg0NzhbJ3RvU3RyaW5nJ10oKSxfMHgxMDQyM2Q9XzB4NTQ4ZDk5W18weDMwMDZkZCgweGI2KV18fHt9LF8weDNkNzk5ZD1fMHg1NDhkOTlbXzB4MzAwNmRkKDB4YWYpXSxfMHgzYmRkMjU9XzB4NTQ4ZDk5Wydpc0V4cHJlc3Npb25Ub0V2YWx1YXRlJ107dHJ5e3ZhciBfMHg1NWY2Mjc9dGhpc1tfMHgzMDA2ZGQoMHhlZCldKF8weDJmY2IwYiksXzB4Mjc0Zjk5PV8weDUzMDYzMztfMHg1NWY2MjcmJl8weDI3NGY5OVsweDBdPT09J1xcXFx4MjcnJiYoXzB4Mjc0Zjk5PV8weDI3NGY5OVtfMHgzMDA2ZGQoMHgxNGMpXSgweDEsXzB4Mjc0Zjk5W18weDMwMDZkZCgweGY1KV0tMHgyKSk7dmFyIF8weDZiMjhlYz1fMHg1NDhkOTlbXzB4MzAwNmRkKDB4YjYpXT1fMHgxMDQyM2RbXzB4MzAwNmRkKDB4MTBhKStfMHgyNzRmOTldO18weDZiMjhlYyYmKF8weDU0OGQ5OVtfMHgzMDA2ZGQoMHhhZildPV8weDU0OGQ5OVsnZGVwdGgnXSsweDEpLF8weDU0OGQ5OVtfMHgzMDA2ZGQoMHgxMzQpXT0hIV8weDZiMjhlYzt2YXIgXzB4NDM0YzM9dHlwZW9mIF8weDUzODQ3OD09XzB4MzAwNmRkKDB4MTI2KSxfMHhkNDQ0MDc9eyduYW1lJzpfMHg0MzRjM3x8XzB4NTVmNjI3P18weDUzMDYzMzp0aGlzWydfcHJvcGVydHlOYW1lJ10oXzB4NTMwNjMzKX07aWYoXzB4NDM0YzMmJihfMHhkNDQ0MDdbXzB4MzAwNmRkKDB4MTI2KV09ITB4MCksIShfMHg1MGRmMzI9PT1fMHgzMDA2ZGQoMHgxMDMpfHxfMHg1MGRmMzI9PT1fMHgzMDA2ZGQoMHgxMzYpKSl7dmFyIF8weGIyMTQ5OD10aGlzW18weDMwMDZkZCgweDE1OSldKF8weDJmY2IwYixfMHg1Mzg0NzgpO2lmKF8weGIyMTQ5OCYmKF8weGIyMTQ5OFtfMHgzMDA2ZGQoMHgxNTUpXSYmKF8weGQ0NDQwN1tfMHgzMDA2ZGQoMHgxMzEpXT0hMHgwKSxfMHhiMjE0OThbXzB4MzAwNmRkKDB4MTNkKV0mJiFfMHg2YjI4ZWMmJiFfMHg1NDhkOTlbXzB4MzAwNmRkKDB4ZGIpXSkpcmV0dXJuIF8weGQ0NDQwN1tfMHgzMDA2ZGQoMHgxODMpXT0hMHgwLHRoaXNbXzB4MzAwNmRkKDB4OTMpXShfMHhkNDQ0MDcsXzB4NTQ4ZDk5KSxfMHhkNDQ0MDc7fXZhciBfMHgyZDQ4YTA7dHJ5e18weDJkNDhhMD1fMHhiOWUwMjkoXzB4MmZjYjBiLF8weDUzODQ3OCk7fWNhdGNoKF8weDI2NTUyZSl7cmV0dXJuIF8weGQ0NDQwNz17J25hbWUnOl8weDUzMDYzMywndHlwZSc6J3Vua25vd24nLCdlcnJvcic6XzB4MjY1NTJlW18weDMwMDZkZCgweDEyZCldfSx0aGlzW18weDMwMDZkZCgweDkzKV0oXzB4ZDQ0NDA3LF8weDU0OGQ5OSksXzB4ZDQ0NDA3O312YXIgXzB4NDg0NGMyPXRoaXNbXzB4MzAwNmRkKDB4MTdmKV0oXzB4MmQ0OGEwKSxfMHgyZmNkMTQ9dGhpc1tfMHgzMDA2ZGQoMHhmNCldKF8weDQ4NDRjMik7aWYoXzB4ZDQ0NDA3W18weDMwMDZkZCgweDE2MildPV8weDQ4NDRjMixfMHgyZmNkMTQpdGhpc1tfMHgzMDA2ZGQoMHg5MyldKF8weGQ0NDQwNyxfMHg1NDhkOTksXzB4MmQ0OGEwLGZ1bmN0aW9uKCl7dmFyIF8weDQzOTQ4OT1fMHgzMDA2ZGQ7XzB4ZDQ0NDA3Wyd2YWx1ZSddPV8weDJkNDhhMFtfMHg0Mzk0ODkoMHgxNWMpXSgpLCFfMHg2YjI4ZWMmJl8weDIwZjBkOVtfMHg0Mzk0ODkoMHgxNWEpXShfMHg0ODQ0YzIsXzB4ZDQ0NDA3LF8weDU0OGQ5OSx7fSk7fSk7ZWxzZXt2YXIgXzB4MzFkMWM3PV8weDU0OGQ5OVtfMHgzMDA2ZGQoMHgxNTYpXSYmXzB4NTQ4ZDk5WydsZXZlbCddPF8weDU0OGQ5OVsnYXV0b0V4cGFuZE1heERlcHRoJ10mJl8weDU0OGQ5OVtfMHgzMDA2ZGQoMHhhNSldW18weDMwMDZkZCgweDE3NildKF8weDJkNDhhMCk8MHgwJiZfMHg0ODQ0YzIhPT0nZnVuY3Rpb24nJiZfMHg1NDhkOTlbXzB4MzAwNmRkKDB4YTkpXTxfMHg1NDhkOTlbXzB4MzAwNmRkKDB4MTRlKV07XzB4MzFkMWM3fHxfMHg1NDhkOTlbJ2xldmVsJ108XzB4M2Q3OTlkfHxfMHg2YjI4ZWM/KHRoaXNbXzB4MzAwNmRkKDB4ZTgpXShfMHhkNDQ0MDcsXzB4MmQ0OGEwLF8weDU0OGQ5OSxfMHg2YjI4ZWN8fHt9KSx0aGlzW18weDMwMDZkZCgweGJiKV0oXzB4MmQ0OGEwLF8weGQ0NDQwNykpOnRoaXNbXzB4MzAwNmRkKDB4OTMpXShfMHhkNDQ0MDcsXzB4NTQ4ZDk5LF8weDJkNDhhMCxmdW5jdGlvbigpe3ZhciBfMHg1ZTFmN2M9XzB4MzAwNmRkO18weDQ4NDRjMj09PV8weDVlMWY3YygweGZlKXx8XzB4NDg0NGMyPT09XzB4NWUxZjdjKDB4OGYpfHwoZGVsZXRlIF8weGQ0NDQwN1tfMHg1ZTFmN2MoMHhhNyldLF8weGQ0NDQwN1snY2FwcGVkJ109ITB4MCk7fSk7fXJldHVybiBfMHhkNDQ0MDc7fWZpbmFsbHl7XzB4NTQ4ZDk5W18weDMwMDZkZCgweGI2KV09XzB4MTA0MjNkLF8weDU0OGQ5OVtfMHgzMDA2ZGQoMHhhZildPV8weDNkNzk5ZCxfMHg1NDhkOTlbXzB4MzAwNmRkKDB4MTM0KV09XzB4M2JkZDI1O319WydfY2FwSWZTdHJpbmcnXShfMHgxZTRkMmUsXzB4Mjk0YWM0LF8weDM2N2JmYixfMHgyMGJhMDYpe3ZhciBfMHg1OGU5NjY9XzB4ZDVhNTlkLF8weDNkMzE3Yz1fMHgyMGJhMDZbJ3N0ckxlbmd0aCddfHxfMHgzNjdiZmJbXzB4NThlOTY2KDB4ZWIpXTtpZigoXzB4MWU0ZDJlPT09XzB4NThlOTY2KDB4YjcpfHxfMHgxZTRkMmU9PT1fMHg1OGU5NjYoMHgxNDYpKSYmXzB4Mjk0YWM0Wyd2YWx1ZSddKXtsZXQgXzB4NWUxMWY5PV8weDI5NGFjNFtfMHg1OGU5NjYoMHhhNyldW18weDU4ZTk2NigweGY1KV07XzB4MzY3YmZiW18weDU4ZTk2NigweDExNSldKz1fMHg1ZTExZjksXzB4MzY3YmZiW18weDU4ZTk2NigweDExNSldPl8weDM2N2JmYltfMHg1OGU5NjYoMHhjZildPyhfMHgyOTRhYzRbXzB4NThlOTY2KDB4MTBkKV09JycsZGVsZXRlIF8weDI5NGFjNFtfMHg1OGU5NjYoMHhhNyldKTpfMHg1ZTExZjk+XzB4M2QzMTdjJiYoXzB4Mjk0YWM0W18weDU4ZTk2NigweDEwZCldPV8weDI5NGFjNFtfMHg1OGU5NjYoMHhhNyldWydzdWJzdHInXSgweDAsXzB4M2QzMTdjKSxkZWxldGUgXzB4Mjk0YWM0W18weDU4ZTk2NigweGE3KV0pO319W18weGQ1YTU5ZCgweGVkKV0oXzB4NDgzZjUyKXt2YXIgXzB4MWNkMDUxPV8weGQ1YTU5ZDtyZXR1cm4hIShfMHg0ODNmNTImJl8weDFjNmViMFtfMHgxY2QwNTEoMHhkMSldJiZ0aGlzWydfb2JqZWN0VG9TdHJpbmcnXShfMHg0ODNmNTIpPT09XzB4MWNkMDUxKDB4MTMyKSYmXzB4NDgzZjUyW18weDFjZDA1MSgweDkyKV0pO31bXzB4ZDVhNTlkKDB4ZDYpXShfMHg0YWYzM2Ipe3ZhciBfMHg1NTI5NDc9XzB4ZDVhNTlkO2lmKF8weDRhZjMzYltfMHg1NTI5NDcoMHhlNildKC9eXFxcXGQrJC8pKXJldHVybiBfMHg0YWYzM2I7dmFyIF8weDVlZGM0Nzt0cnl7XzB4NWVkYzQ3PUpTT05bXzB4NTUyOTQ3KDB4ZDUpXSgnJytfMHg0YWYzM2IpO31jYXRjaHtfMHg1ZWRjNDc9J1xcXFx4MjInK3RoaXNbXzB4NTUyOTQ3KDB4MTQ3KV0oXzB4NGFmMzNiKSsnXFxcXHgyMic7fXJldHVybiBfMHg1ZWRjNDdbJ21hdGNoJ10oL15cXFwiKFthLXpBLVpfXVthLXpBLVpfMC05XSopXFxcIiQvKT9fMHg1ZWRjNDc9XzB4NWVkYzQ3W18weDU1Mjk0NygweDE0YyldKDB4MSxfMHg1ZWRjNDdbXzB4NTUyOTQ3KDB4ZjUpXS0weDIpOl8weDVlZGM0Nz1fMHg1ZWRjNDdbXzB4NTUyOTQ3KDB4YTEpXSgvJy9nLCdcXFxceDVjXFxcXHgyNycpWydyZXBsYWNlJ10oL1xcXFxcXFxcXFxcIi9nLCdcXFxceDIyJylbJ3JlcGxhY2UnXSgvKF5cXFwifFxcXCIkKS9nLCdcXFxceDI3JyksXzB4NWVkYzQ3O31bJ19wcm9jZXNzVHJlZU5vZGVSZXN1bHQnXShfMHgyNDMwOGEsXzB4ZjhkYzRjLF8weDIxNjA5NCxfMHgyZDBlYTEpe3ZhciBfMHg0NzA3OGU9XzB4ZDVhNTlkO3RoaXNbXzB4NDcwNzhlKDB4YzgpXShfMHgyNDMwOGEsXzB4ZjhkYzRjKSxfMHgyZDBlYTEmJl8weDJkMGVhMSgpLHRoaXNbXzB4NDcwNzhlKDB4YmIpXShfMHgyMTYwOTQsXzB4MjQzMDhhKSx0aGlzW18weDQ3MDc4ZSgweGRhKV0oXzB4MjQzMDhhLF8weGY4ZGM0Yyk7fVsnX3RyZWVOb2RlUHJvcGVydGllc0JlZm9yZUZ1bGxWYWx1ZSddKF8weDk2NzM1OCxfMHg2YTdjMDEpe3ZhciBfMHg1NTM4ZTg9XzB4ZDVhNTlkO3RoaXNbXzB4NTUzOGU4KDB4ZjcpXShfMHg5NjczNTgsXzB4NmE3YzAxKSx0aGlzW18weDU1MzhlOCgweGFiKV0oXzB4OTY3MzU4LF8weDZhN2MwMSksdGhpc1tfMHg1NTM4ZTgoMHhhNildKF8weDk2NzM1OCxfMHg2YTdjMDEpLHRoaXNbXzB4NTUzOGU4KDB4MTZmKV0oXzB4OTY3MzU4LF8weDZhN2MwMSk7fVtfMHhkNWE1OWQoMHhmNyldKF8weDI4MWE2OSxfMHgxZmRhZjMpe31bXzB4ZDVhNTlkKDB4YWIpXShfMHg1NWUxMzIsXzB4Mjg3ZDM4KXt9W18weGQ1YTU5ZCgweDE0YSldKF8weDQxNWM3YixfMHg1ODFiNzcpe31bJ19pc1VuZGVmaW5lZCddKF8weGQzYzVhZSl7cmV0dXJuIF8weGQzYzVhZT09PXRoaXNbJ191bmRlZmluZWQnXTt9W18weGQ1YTU5ZCgweGRhKV0oXzB4MTAwZWUxLF8weDFlNDdhZCl7dmFyIF8weDE5MmYyOD1fMHhkNWE1OWQ7dGhpc1snX3NldE5vZGVMYWJlbCddKF8weDEwMGVlMSxfMHgxZTQ3YWQpLHRoaXNbJ19zZXROb2RlRXhwYW5kYWJsZVN0YXRlJ10oXzB4MTAwZWUxKSxfMHgxZTQ3YWRbXzB4MTkyZjI4KDB4MTc0KV0mJnRoaXNbXzB4MTkyZjI4KDB4MTZiKV0oXzB4MTAwZWUxKSx0aGlzW18weDE5MmYyOCgweDlmKV0oXzB4MTAwZWUxLF8weDFlNDdhZCksdGhpc1tfMHgxOTJmMjgoMHgxMjQpXShfMHgxMDBlZTEsXzB4MWU0N2FkKSx0aGlzWydfY2xlYW5Ob2RlJ10oXzB4MTAwZWUxKTt9WydfYWRkaXRpb25hbE1ldGFkYXRhJ10oXzB4MTNlZWMzLF8weDMxYzk3NCl7dmFyIF8weDU3NzVmND1fMHhkNWE1OWQ7dHJ5e18weDEzZWVjMyYmdHlwZW9mIF8weDEzZWVjM1tfMHg1Nzc1ZjQoMHhmNSldPT0nbnVtYmVyJyYmKF8weDMxYzk3NFtfMHg1Nzc1ZjQoMHhmNSldPV8weDEzZWVjM1tfMHg1Nzc1ZjQoMHhmNSldKTt9Y2F0Y2h7fWlmKF8weDMxYzk3NFtfMHg1Nzc1ZjQoMHgxNjIpXT09PSdudW1iZXInfHxfMHgzMWM5NzRbJ3R5cGUnXT09PV8weDU3NzVmNCgweGVlKSl7aWYoaXNOYU4oXzB4MzFjOTc0W18weDU3NzVmNCgweGE3KV0pKV8weDMxYzk3NFtfMHg1Nzc1ZjQoMHgxMjUpXT0hMHgwLGRlbGV0ZSBfMHgzMWM5NzRbXzB4NTc3NWY0KDB4YTcpXTtlbHNlIHN3aXRjaChfMHgzMWM5NzRbXzB4NTc3NWY0KDB4YTcpXSl7Y2FzZSBOdW1iZXJbXzB4NTc3NWY0KDB4OTYpXTpfMHgzMWM5NzRbJ3Bvc2l0aXZlSW5maW5pdHknXT0hMHgwLGRlbGV0ZSBfMHgzMWM5NzRbJ3ZhbHVlJ107YnJlYWs7Y2FzZSBOdW1iZXJbXzB4NTc3NWY0KDB4MTIzKV06XzB4MzFjOTc0W18weDU3NzVmNCgweDE3MyldPSEweDAsZGVsZXRlIF8weDMxYzk3NFtfMHg1Nzc1ZjQoMHhhNyldO2JyZWFrO2Nhc2UgMHgwOnRoaXNbJ19pc05lZ2F0aXZlWmVybyddKF8weDMxYzk3NFtfMHg1Nzc1ZjQoMHhhNyldKSYmKF8weDMxYzk3NFtfMHg1Nzc1ZjQoMHgxNjgpXT0hMHgwKTticmVhazt9fWVsc2UgXzB4MzFjOTc0W18weDU3NzVmNCgweDE2MildPT09XzB4NTc3NWY0KDB4MTQ0KSYmdHlwZW9mIF8weDEzZWVjM1tfMHg1Nzc1ZjQoMHgxN2IpXT09XzB4NTc3NWY0KDB4YjcpJiZfMHgxM2VlYzNbXzB4NTc3NWY0KDB4MTdiKV0mJl8weDMxYzk3NFtfMHg1Nzc1ZjQoMHgxN2IpXSYmXzB4MTNlZWMzW18weDU3NzVmNCgweDE3YildIT09XzB4MzFjOTc0W18weDU3NzVmNCgweDE3YildJiYoXzB4MzFjOTc0W18weDU3NzVmNCgweGJjKV09XzB4MTNlZWMzW18weDU3NzVmNCgweDE3YildKTt9W18weGQ1YTU5ZCgweDExOCldKF8weDNmNjJiZCl7cmV0dXJuIDB4MS9fMHgzZjYyYmQ9PT1OdW1iZXJbJ05FR0FUSVZFX0lORklOSVRZJ107fVtfMHhkNWE1OWQoMHgxNmIpXShfMHgyZmU2ZWQpe3ZhciBfMHgzODdlMmM9XzB4ZDVhNTlkOyFfMHgyZmU2ZWRbXzB4Mzg3ZTJjKDB4ZGUpXXx8IV8weDJmZTZlZFtfMHgzODdlMmMoMHhkZSldW18weDM4N2UyYygweGY1KV18fF8weDJmZTZlZFsndHlwZSddPT09XzB4Mzg3ZTJjKDB4MTAzKXx8XzB4MmZlNmVkW18weDM4N2UyYygweDE2MildPT09XzB4Mzg3ZTJjKDB4ZDEpfHxfMHgyZmU2ZWRbXzB4Mzg3ZTJjKDB4MTYyKV09PT1fMHgzODdlMmMoMHgxODYpfHxfMHgyZmU2ZWRbJ3Byb3BzJ11bJ3NvcnQnXShmdW5jdGlvbihfMHgzYjliMjQsXzB4NTY2NzFjKXt2YXIgXzB4NTA5ZmE4PV8weDM4N2UyYyxfMHg0ZjY2Nzg9XzB4M2I5YjI0W18weDUwOWZhOCgweDE3YildWyd0b0xvd2VyQ2FzZSddKCksXzB4MzkwZjhkPV8weDU2NjcxY1tfMHg1MDlmYTgoMHgxN2IpXVsndG9Mb3dlckNhc2UnXSgpO3JldHVybiBfMHg0ZjY2Nzg8XzB4MzkwZjhkPy0weDE6XzB4NGY2Njc4Pl8weDM5MGY4ZD8weDE6MHgwO30pO31bJ19hZGRGdW5jdGlvbnNOb2RlJ10oXzB4ZGVmNDMwLF8weDFkNGY0Nyl7dmFyIF8weDFhMDA0Mz1fMHhkNWE1OWQ7aWYoIShfMHgxZDRmNDdbXzB4MWEwMDQzKDB4YjkpXXx8IV8weGRlZjQzMFtfMHgxYTAwNDMoMHhkZSldfHwhXzB4ZGVmNDMwW18weDFhMDA0MygweGRlKV1bJ2xlbmd0aCddKSl7Zm9yKHZhciBfMHgxNWFmYWM9W10sXzB4MTA5YTU5PVtdLF8weDI4MWY3YT0weDAsXzB4NWNkZmJjPV8weGRlZjQzMFtfMHgxYTAwNDMoMHhkZSldWydsZW5ndGgnXTtfMHgyODFmN2E8XzB4NWNkZmJjO18weDI4MWY3YSsrKXt2YXIgXzB4YTgwMzM1PV8weGRlZjQzMFsncHJvcHMnXVtfMHgyODFmN2FdO18weGE4MDMzNVtfMHgxYTAwNDMoMHgxNjIpXT09PV8weDFhMDA0MygweDE0NCk/XzB4MTVhZmFjWydwdXNoJ10oXzB4YTgwMzM1KTpfMHgxMDlhNTlbXzB4MWEwMDQzKDB4YjEpXShfMHhhODAzMzUpO31pZighKCFfMHgxMDlhNTlbXzB4MWEwMDQzKDB4ZjUpXXx8XzB4MTVhZmFjW18weDFhMDA0MygweGY1KV08PTB4MSkpe18weGRlZjQzMFtfMHgxYTAwNDMoMHhkZSldPV8weDEwOWE1OTt2YXIgXzB4MjBiMWRiPXsnZnVuY3Rpb25zTm9kZSc6ITB4MCwncHJvcHMnOl8weDE1YWZhY307dGhpc1tfMHgxYTAwNDMoMHhmNyldKF8weDIwYjFkYixfMHgxZDRmNDcpLHRoaXNbJ19zZXROb2RlTGFiZWwnXShfMHgyMGIxZGIsXzB4MWQ0ZjQ3KSx0aGlzW18weDFhMDA0MygweDExMildKF8weDIwYjFkYiksdGhpc1snX3NldE5vZGVQZXJtaXNzaW9ucyddKF8weDIwYjFkYixfMHgxZDRmNDcpLF8weDIwYjFkYlsnaWQnXSs9J1xcXFx4MjBmJyxfMHhkZWY0MzBbXzB4MWEwMDQzKDB4ZGUpXVtfMHgxYTAwNDMoMHhlZildKF8weDIwYjFkYik7fX19WydfYWRkTG9hZE5vZGUnXShfMHgyZDRlZDcsXzB4MTBjNjlmKXt9W18weGQ1YTU5ZCgweDExMildKF8weDFlYjU1YSl7fVtfMHhkNWE1OWQoMHhkMCldKF8weDQ3MTk5MSl7dmFyIF8weDIzNWQ4Mz1fMHhkNWE1OWQ7cmV0dXJuIEFycmF5W18weDIzNWQ4MygweDk3KV0oXzB4NDcxOTkxKXx8dHlwZW9mIF8weDQ3MTk5MT09XzB4MjM1ZDgzKDB4MTEwKSYmdGhpc1tfMHgyMzVkODMoMHgxNDcpXShfMHg0NzE5OTEpPT09XzB4MjM1ZDgzKDB4Y2EpO31bXzB4ZDVhNTlkKDB4MTZmKV0oXzB4NTczMzYzLF8weDM3OGI1Myl7fVtfMHhkNWE1OWQoMHgxN2MpXShfMHgxYmQ2ZGEpe3ZhciBfMHhiZTIxZjQ9XzB4ZDVhNTlkO2RlbGV0ZSBfMHgxYmQ2ZGFbXzB4YmUyMWY0KDB4MTc4KV0sZGVsZXRlIF8weDFiZDZkYVsnX2hhc1NldE9uSXRzUGF0aCddLGRlbGV0ZSBfMHgxYmQ2ZGFbXzB4YmUyMWY0KDB4MTQ5KV07fVtfMHhkNWE1OWQoMHhhNildKF8weDM1ZDFlZixfMHg0ZWQ1M2Ipe319bGV0IF8weDUzZDk3ND1uZXcgXzB4NGEyMTM3KCksXzB4M2ViMWM1PXsncHJvcHMnOjB4NjQsJ2VsZW1lbnRzJzoweDY0LCdzdHJMZW5ndGgnOjB4NDAwKjB4MzIsJ3RvdGFsU3RyTGVuZ3RoJzoweDQwMCoweDMyLCdhdXRvRXhwYW5kTGltaXQnOjB4MTM4OCwnYXV0b0V4cGFuZE1heERlcHRoJzoweGF9LF8weDU1MDYwMj17J3Byb3BzJzoweDUsJ2VsZW1lbnRzJzoweDUsJ3N0ckxlbmd0aCc6MHgxMDAsJ3RvdGFsU3RyTGVuZ3RoJzoweDEwMCoweDMsJ2F1dG9FeHBhbmRMaW1pdCc6MHgxZSwnYXV0b0V4cGFuZE1heERlcHRoJzoweDJ9O2Z1bmN0aW9uIF8weDI1OWQ2YyhfMHgyZTM3NzksXzB4ZDkxN2IyLF8weGIzMjdhYyxfMHgyNDcwN2YsXzB4NGFkNTVlLF8weDRmNTQ2Nil7dmFyIF8weDRiOWY3Mj1fMHhkNWE1OWQ7bGV0IF8weDRlYTQ3MixfMHg1YjQxOTc7dHJ5e18weDViNDE5Nz1fMHg1N2Q5NDUoKSxfMHg0ZWE0NzI9XzB4NTFkZTE4W18weGQ5MTdiMl0sIV8weDRlYTQ3Mnx8XzB4NWI0MTk3LV8weDRlYTQ3MlsndHMnXT4weDFmNCYmXzB4NGVhNDcyW18weDRiOWY3MigweDExZildJiZfMHg0ZWE0NzJbXzB4NGI5ZjcyKDB4MTM3KV0vXzB4NGVhNDcyW18weDRiOWY3MigweDExZildPDB4NjQ/KF8weDUxZGUxOFtfMHhkOTE3YjJdPV8weDRlYTQ3Mj17J2NvdW50JzoweDAsJ3RpbWUnOjB4MCwndHMnOl8weDViNDE5N30sXzB4NTFkZTE4W18weDRiOWY3MigweDE3YSldPXt9KTpfMHg1YjQxOTctXzB4NTFkZTE4W18weDRiOWY3MigweDE3YSldWyd0cyddPjB4MzImJl8weDUxZGUxOFtfMHg0YjlmNzIoMHgxN2EpXVtfMHg0YjlmNzIoMHgxMWYpXSYmXzB4NTFkZTE4W18weDRiOWY3MigweDE3YSldW18weDRiOWY3MigweDEzNyldL18weDUxZGUxOFtfMHg0YjlmNzIoMHgxN2EpXVsnY291bnQnXTwweDY0JiYoXzB4NTFkZTE4W18weDRiOWY3MigweDE3YSldPXt9KTtsZXQgXzB4MTBkMTM0PVtdLF8weDU5Y2I2Nz1fMHg0ZWE0NzJbXzB4NGI5ZjcyKDB4MTQyKV18fF8weDUxZGUxOFtfMHg0YjlmNzIoMHgxN2EpXVtfMHg0YjlmNzIoMHgxNDIpXT9fMHg1NTA2MDI6XzB4M2ViMWM1LF8weDU5NDAwMz1fMHg1ZTMwMDk9Pnt2YXIgXzB4MmQ3NzAwPV8weDRiOWY3MjtsZXQgXzB4YTA3NzVmPXt9O3JldHVybiBfMHhhMDc3NWZbXzB4MmQ3NzAwKDB4ZGUpXT1fMHg1ZTMwMDlbXzB4MmQ3NzAwKDB4ZGUpXSxfMHhhMDc3NWZbXzB4MmQ3NzAwKDB4Y2QpXT1fMHg1ZTMwMDlbJ2VsZW1lbnRzJ10sXzB4YTA3NzVmWydzdHJMZW5ndGgnXT1fMHg1ZTMwMDlbJ3N0ckxlbmd0aCddLF8weGEwNzc1ZltfMHgyZDc3MDAoMHhjZildPV8weDVlMzAwOVsndG90YWxTdHJMZW5ndGgnXSxfMHhhMDc3NWZbXzB4MmQ3NzAwKDB4MTRlKV09XzB4NWUzMDA5W18weDJkNzcwMCgweDE0ZSldLF8weGEwNzc1ZltfMHgyZDc3MDAoMHhlOSldPV8weDVlMzAwOVtfMHgyZDc3MDAoMHhlOSldLF8weGEwNzc1ZltfMHgyZDc3MDAoMHgxNzQpXT0hMHgxLF8weGEwNzc1Zlsnbm9GdW5jdGlvbnMnXT0hXzB4NTUyOTE5LF8weGEwNzc1ZltfMHgyZDc3MDAoMHhhZildPTB4MSxfMHhhMDc3NWZbXzB4MmQ3NzAwKDB4ZTApXT0weDAsXzB4YTA3NzVmW18weDJkNzcwMCgweDkxKV09XzB4MmQ3NzAwKDB4MThhKSxfMHhhMDc3NWZbXzB4MmQ3NzAwKDB4ZmIpXT1fMHgyZDc3MDAoMHgxM2EpLF8weGEwNzc1ZltfMHgyZDc3MDAoMHgxNTYpXT0hMHgwLF8weGEwNzc1ZlsnYXV0b0V4cGFuZFByZXZpb3VzT2JqZWN0cyddPVtdLF8weGEwNzc1ZltfMHgyZDc3MDAoMHhhOSldPTB4MCxfMHhhMDc3NWZbXzB4MmQ3NzAwKDB4ZGIpXT0hMHgwLF8weGEwNzc1ZltfMHgyZDc3MDAoMHgxMTUpXT0weDAsXzB4YTA3NzVmW18weDJkNzcwMCgweDE4MildPXsnY3VycmVudCc6dm9pZCAweDAsJ3BhcmVudCc6dm9pZCAweDAsJ2luZGV4JzoweDB9LF8weGEwNzc1Zjt9O2Zvcih2YXIgXzB4NGUzZWYxPTB4MDtfMHg0ZTNlZjE8XzB4NGFkNTVlW18weDRiOWY3MigweGY1KV07XzB4NGUzZWYxKyspXzB4MTBkMTM0W18weDRiOWY3MigweGIxKV0oXzB4NTNkOTc0W18weDRiOWY3MigweGU4KV0oeyd0aW1lTm9kZSc6XzB4MmUzNzc5PT09XzB4NGI5ZjcyKDB4MTM3KXx8dm9pZCAweDB9LF8weDRhZDU1ZVtfMHg0ZTNlZjFdLF8weDU5NDAwMyhfMHg1OWNiNjcpLHt9KSk7aWYoXzB4MmUzNzc5PT09XzB4NGI5ZjcyKDB4YWQpfHxfMHgyZTM3Nzk9PT1fMHg0YjlmNzIoMHgxNmEpKXtsZXQgXzB4NTk3YzBkPUVycm9yW18weDRiOWY3MigweDE1MyldO3RyeXtFcnJvclsnc3RhY2tUcmFjZUxpbWl0J109MHgxLzB4MCxfMHgxMGQxMzRbJ3B1c2gnXShfMHg1M2Q5NzRbXzB4NGI5ZjcyKDB4ZTgpXSh7J3N0YWNrTm9kZSc6ITB4MH0sbmV3IEVycm9yKClbXzB4NGI5ZjcyKDB4MTYzKV0sXzB4NTk0MDAzKF8weDU5Y2I2NykseydzdHJMZW5ndGgnOjB4MS8weDB9KSk7fWZpbmFsbHl7RXJyb3JbXzB4NGI5ZjcyKDB4MTUzKV09XzB4NTk3YzBkO319cmV0dXJueydtZXRob2QnOl8weDRiOWY3MigweGQ3KSwndmVyc2lvbic6XzB4NTMzYzljLCdhcmdzJzpbeyd0cyc6XzB4YjMyN2FjLCdzZXNzaW9uJzpfMHgyNDcwN2YsJ2FyZ3MnOl8weDEwZDEzNCwnaWQnOl8weGQ5MTdiMiwnY29udGV4dCc6XzB4NGY1NDY2fV19O31jYXRjaChfMHg1NDEwODIpe3JldHVybnsnbWV0aG9kJzonbG9nJywndmVyc2lvbic6XzB4NTMzYzljLCdhcmdzJzpbeyd0cyc6XzB4YjMyN2FjLCdzZXNzaW9uJzpfMHgyNDcwN2YsJ2FyZ3MnOlt7J3R5cGUnOid1bmtub3duJywnZXJyb3InOl8weDU0MTA4MiYmXzB4NTQxMDgyWydtZXNzYWdlJ119XSwnaWQnOl8weGQ5MTdiMiwnY29udGV4dCc6XzB4NGY1NDY2fV19O31maW5hbGx5e3RyeXtpZihfMHg0ZWE0NzImJl8weDViNDE5Nyl7bGV0IF8weDI0ODQxZT1fMHg1N2Q5NDUoKTtfMHg0ZWE0NzJbJ2NvdW50J10rKyxfMHg0ZWE0NzJbXzB4NGI5ZjcyKDB4MTM3KV0rPV8weDFhMWI4MChfMHg1YjQxOTcsXzB4MjQ4NDFlKSxfMHg0ZWE0NzJbJ3RzJ109XzB4MjQ4NDFlLF8weDUxZGUxOFtfMHg0YjlmNzIoMHgxN2EpXVtfMHg0YjlmNzIoMHgxMWYpXSsrLF8weDUxZGUxOFsnaGl0cyddW18weDRiOWY3MigweDEzNyldKz1fMHgxYTFiODAoXzB4NWI0MTk3LF8weDI0ODQxZSksXzB4NTFkZTE4W18weDRiOWY3MigweDE3YSldWyd0cyddPV8weDI0ODQxZSwoXzB4NGVhNDcyW18weDRiOWY3MigweDExZildPjB4MzJ8fF8weDRlYTQ3MlsndGltZSddPjB4NjQpJiYoXzB4NGVhNDcyW18weDRiOWY3MigweDE0MildPSEweDApLChfMHg1MWRlMThbJ2hpdHMnXVsnY291bnQnXT4weDNlOHx8XzB4NTFkZTE4WydoaXRzJ11bXzB4NGI5ZjcyKDB4MTM3KV0+MHgxMmMpJiYoXzB4NTFkZTE4WydoaXRzJ11bJ3JlZHVjZUxpbWl0cyddPSEweDApO319Y2F0Y2h7fX19cmV0dXJuIF8weDI1OWQ2Yzt9KChfMHg1ZTQ3YWQsXzB4Mzc4ZDEzLF8weDM2YzVhZixfMHgyYWQ0MDAsXzB4M2I2YzQxLF8weDNkNDNjNixfMHgyZmZiYTAsXzB4YjlmM2IsXzB4MTgwN2NhLF8weDM5MjgxYyxfMHhhYjgzZDcpPT57dmFyIF8weDNlNzIyZj1fMHgyNTIxZWQ7aWYoXzB4NWU0N2FkW18weDNlNzIyZigweGY4KV0pcmV0dXJuIF8weDVlNDdhZFtfMHgzZTcyMmYoMHhmOCldO2lmKCFYKF8weDVlNDdhZCxfMHhiOWYzYixfMHgzYjZjNDEpKXJldHVybiBfMHg1ZTQ3YWRbJ19jb25zb2xlX25pbmphJ109eydjb25zb2xlTG9nJzooKT0+e30sJ2NvbnNvbGVUcmFjZSc6KCk9Pnt9LCdjb25zb2xlVGltZSc6KCk9Pnt9LCdjb25zb2xlVGltZUVuZCc6KCk9Pnt9LCdhdXRvTG9nJzooKT0+e30sJ2F1dG9Mb2dNYW55JzooKT0+e30sJ2F1dG9UcmFjZU1hbnknOigpPT57fSwnY292ZXJhZ2UnOigpPT57fSwnYXV0b1RyYWNlJzooKT0+e30sJ2F1dG9UaW1lJzooKT0+e30sJ2F1dG9UaW1lRW5kJzooKT0+e319LF8weDVlNDdhZFtfMHgzZTcyMmYoMHhmOCldO2xldCBfMHg0OGI2ZGI9QihfMHg1ZTQ3YWQpLF8weDU3YWMxMj1fMHg0OGI2ZGJbJ2VsYXBzZWQnXSxfMHhlMzNkYjk9XzB4NDhiNmRiW18weDNlNzIyZigweDEyMCldLF8weDUyNmFmMD1fMHg0OGI2ZGJbXzB4M2U3MjJmKDB4MTVkKV0sXzB4MWRkNTdjPXsnaGl0cyc6e30sJ3RzJzp7fX0sXzB4M2Q3NjhlPUooXzB4NWU0N2FkLF8weDE4MDdjYSxfMHgxZGQ1N2MsXzB4M2Q0M2M2KSxfMHg1ZjIzNjY9XzB4MzYxNTQ3PT57XzB4MWRkNTdjWyd0cyddW18weDM2MTU0N109XzB4ZTMzZGI5KCk7fSxfMHgyOTU4ZjE9KF8weDM2ZGRhNCxfMHg1OGM4MDkpPT57dmFyIF8weDhlMzZlYz1fMHgzZTcyMmY7bGV0IF8weDNlOTY2Mj1fMHgxZGQ1N2NbJ3RzJ11bXzB4NThjODA5XTtpZihkZWxldGUgXzB4MWRkNTdjWyd0cyddW18weDU4YzgwOV0sXzB4M2U5NjYyKXtsZXQgXzB4YzI1YjI9XzB4NTdhYzEyKF8weDNlOTY2MixfMHhlMzNkYjkoKSk7XzB4MzgzM2MzKF8weDNkNzY4ZShfMHg4ZTM2ZWMoMHgxMzcpLF8weDM2ZGRhNCxfMHg1MjZhZjAoKSxfMHg0ZTRhODAsW18weGMyNWIyXSxfMHg1OGM4MDkpKTt9fSxfMHg1NWUzNTM9XzB4M2MyNzI2PT57dmFyIF8weDRjMThiZj1fMHgzZTcyMmYsXzB4NDcwODMwO3JldHVybiBfMHgzYjZjNDE9PT0nbmV4dC5qcycmJl8weDVlNDdhZFtfMHg0YzE4YmYoMHgxMWIpXSYmKChfMHg0NzA4MzA9XzB4M2MyNzI2PT1udWxsP3ZvaWQgMHgwOl8weDNjMjcyNltfMHg0YzE4YmYoMHgxNTcpXSk9PW51bGw/dm9pZCAweDA6XzB4NDcwODMwW18weDRjMThiZigweGY1KV0pJiYoXzB4M2MyNzI2W18weDRjMThiZigweDE1NyldWzB4MF1bJ29yaWdpbiddPV8weDVlNDdhZFsnb3JpZ2luJ10pLF8weDNjMjcyNjt9O18weDVlNDdhZFsnX2NvbnNvbGVfbmluamEnXT17J2NvbnNvbGVMb2cnOihfMHhiMWRiMjcsXzB4NGQ3ZmRlKT0+e3ZhciBfMHg0MWZhYzU9XzB4M2U3MjJmO18weDVlNDdhZFtfMHg0MWZhYzUoMHhjYyldWydsb2cnXVsnbmFtZSddIT09XzB4NDFmYWM1KDB4MTE5KSYmXzB4MzgzM2MzKF8weDNkNzY4ZShfMHg0MWZhYzUoMHhkNyksXzB4YjFkYjI3LF8weDUyNmFmMCgpLF8weDRlNGE4MCxfMHg0ZDdmZGUpKTt9LCdjb25zb2xlVHJhY2UnOihfMHgzZDJiM2YsXzB4MjlmNzU4KT0+e3ZhciBfMHgzYmIzNTQ9XzB4M2U3MjJmLF8weDI0ZmQ2OCxfMHg1NjUxMDk7XzB4NWU0N2FkW18weDNiYjM1NCgweGNjKV1bXzB4M2JiMzU0KDB4ZDcpXVsnbmFtZSddIT09XzB4M2JiMzU0KDB4MThlKSYmKChfMHg1NjUxMDk9KF8weDI0ZmQ2OD1fMHg1ZTQ3YWRbJ3Byb2Nlc3MnXSk9PW51bGw/dm9pZCAweDA6XzB4MjRmZDY4Wyd2ZXJzaW9ucyddKSE9bnVsbCYmXzB4NTY1MTA5W18weDNiYjM1NCgweDE4MildJiYoXzB4NWU0N2FkW18weDNiYjM1NCgweDE2OSldPSEweDApLF8weDM4MzNjMyhfMHg1NWUzNTMoXzB4M2Q3NjhlKCd0cmFjZScsXzB4M2QyYjNmLF8weDUyNmFmMCgpLF8weDRlNGE4MCxfMHgyOWY3NTgpKSkpO30sJ2NvbnNvbGVFcnJvcic6KF8weDI5MDVhOCxfMHg1ZTg1ODkpPT57dmFyIF8weDQwNDUxMT1fMHgzZTcyMmY7XzB4NWU0N2FkW18weDQwNDUxMSgweDE2OSldPSEweDAsXzB4MzgzM2MzKF8weDU1ZTM1MyhfMHgzZDc2OGUoXzB4NDA0NTExKDB4MTZhKSxfMHgyOTA1YTgsXzB4NTI2YWYwKCksXzB4NGU0YTgwLF8weDVlODU4OSkpKTt9LCdjb25zb2xlVGltZSc6XzB4MmZiZmJlPT57XzB4NWYyMzY2KF8weDJmYmZiZSk7fSwnY29uc29sZVRpbWVFbmQnOihfMHgyNTk3MzIsXzB4NTU5MTcxKT0+e18weDI5NThmMShfMHg1NTkxNzEsXzB4MjU5NzMyKTt9LCdhdXRvTG9nJzooXzB4NDA1NjhlLF8weDJjZTM0Nik9Pnt2YXIgXzB4NWI5MmQ5PV8weDNlNzIyZjtfMHgzODMzYzMoXzB4M2Q3NjhlKF8weDViOTJkOSgweGQ3KSxfMHgyY2UzNDYsXzB4NTI2YWYwKCksXzB4NGU0YTgwLFtfMHg0MDU2OGVdKSk7fSwnYXV0b0xvZ01hbnknOihfMHgzODI5NjcsXzB4NDYzOWYyKT0+e3ZhciBfMHg1Njk4YWU9XzB4M2U3MjJmO18weDM4MzNjMyhfMHgzZDc2OGUoXzB4NTY5OGFlKDB4ZDcpLF8weDM4Mjk2NyxfMHg1MjZhZjAoKSxfMHg0ZTRhODAsXzB4NDYzOWYyKSk7fSwnYXV0b1RyYWNlJzooXzB4ODVlZGVmLF8weDE5NzFmYyk9Pnt2YXIgXzB4MzIzOTc1PV8weDNlNzIyZjtfMHgzODMzYzMoXzB4NTVlMzUzKF8weDNkNzY4ZShfMHgzMjM5NzUoMHhhZCksXzB4MTk3MWZjLF8weDUyNmFmMCgpLF8weDRlNGE4MCxbXzB4ODVlZGVmXSkpKTt9LCdhdXRvVHJhY2VNYW55JzooXzB4NDIzY2IyLF8weDEzMjYwMSk9Pnt2YXIgXzB4NDI5YmE1PV8weDNlNzIyZjtfMHgzODMzYzMoXzB4NTVlMzUzKF8weDNkNzY4ZShfMHg0MjliYTUoMHhhZCksXzB4NDIzY2IyLF8weDUyNmFmMCgpLF8weDRlNGE4MCxfMHgxMzI2MDEpKSk7fSwnYXV0b1RpbWUnOihfMHg0MDkzYzIsXzB4MTg3MDg5LF8weDNhMzg0Nyk9PntfMHg1ZjIzNjYoXzB4M2EzODQ3KTt9LCdhdXRvVGltZUVuZCc6KF8weGQ2OWRhYSxfMHg0ZTAwY2IsXzB4MTI0NDdiKT0+e18weDI5NThmMShfMHg0ZTAwY2IsXzB4MTI0NDdiKTt9LCdjb3ZlcmFnZSc6XzB4MjhmMGE1PT57dmFyIF8weDU5NjNlMz1fMHgzZTcyMmY7XzB4MzgzM2MzKHsnbWV0aG9kJzpfMHg1OTYzZTMoMHgxM2UpLCd2ZXJzaW9uJzpfMHgzZDQzYzYsJ2FyZ3MnOlt7J2lkJzpfMHgyOGYwYTV9XX0pO319O2xldCBfMHgzODMzYzM9SChfMHg1ZTQ3YWQsXzB4Mzc4ZDEzLF8weDM2YzVhZixfMHgyYWQ0MDAsXzB4M2I2YzQxLF8weDM5MjgxYyxfMHhhYjgzZDcpLF8weDRlNGE4MD1fMHg1ZTQ3YWRbJ19jb25zb2xlX25pbmphX3Nlc3Npb24nXTtyZXR1cm4gXzB4NWU0N2FkW18weDNlNzIyZigweGY4KV07fSkoZ2xvYmFsVGhpcyxfMHgyNTIxZWQoMHgxMGYpLF8weDI1MjFlZCgweGIwKSxfMHgyNTIxZWQoMHhmYyksXzB4MjUyMWVkKDB4MTJjKSwnMS4wLjAnLCcxNzUzNDM1Njk2MTAwJyxfMHgyNTIxZWQoMHhjMyksXzB4MjUyMWVkKDB4MTBlKSwnJywnMScpO1wiKTt9Y2F0Y2goZSl7fX07LyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9mdW5jdGlvbiBvb19vbyhpOnN0cmluZywuLi52OmFueVtdKXt0cnl7b29fY20oKS5jb25zb2xlTG9nKGksIHYpO31jYXRjaChlKXt9IHJldHVybiB2fTtvb19vbzsvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL2Z1bmN0aW9uIG9vX3RyKGk6c3RyaW5nLC4uLnY6YW55W10pe3RyeXtvb19jbSgpLmNvbnNvbGVUcmFjZShpLCB2KTt9Y2F0Y2goZSl7fSByZXR1cm4gdn07b29fdHI7LyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9mdW5jdGlvbiBvb190eChpOnN0cmluZywuLi52OmFueVtdKXt0cnl7b29fY20oKS5jb25zb2xlRXJyb3IoaSwgdik7fWNhdGNoKGUpe30gcmV0dXJuIHZ9O29vX3R4Oy8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovZnVuY3Rpb24gb29fdHModj86c3RyaW5nKTpzdHJpbmd7dHJ5e29vX2NtKCkuY29uc29sZVRpbWUodik7fWNhdGNoKGUpe30gcmV0dXJuIHYgYXMgc3RyaW5nO307b29fdHM7LyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9mdW5jdGlvbiBvb190ZSh2OnN0cmluZ3x1bmRlZmluZWQsIGk6c3RyaW5nKTpzdHJpbmd7dHJ5e29vX2NtKCkuY29uc29sZVRpbWVFbmQodiwgaSk7fWNhdGNoKGUpe30gcmV0dXJuIHYgYXMgc3RyaW5nO307b29fdGU7Lyplc2xpbnQgdW5pY29ybi9uby1hYnVzaXZlLWVzbGludC1kaXNhYmxlOixlc2xpbnQtY29tbWVudHMvZGlzYWJsZS1lbmFibGUtcGFpcjosZXNsaW50LWNvbW1lbnRzL25vLXVubGltaXRlZC1kaXNhYmxlOixlc2xpbnQtY29tbWVudHMvbm8tYWdncmVnYXRpbmctZW5hYmxlOixlc2xpbnQtY29tbWVudHMvbm8tZHVwbGljYXRlLWRpc2FibGU6LGVzbGludC1jb21tZW50cy9uby11bnVzZWQtZGlzYWJsZTosZXNsaW50LWNvbW1lbnRzL25vLXVudXNlZC1lbmFibGU6LCovIl0sIm5hbWVzIjpbIkFsbENvbW11bml0eU1vZHVsZSIsIk1vZHVsZVJlZ2lzdHJ5IiwiRGF0ZVRpbWUiLCJQaW5uZWRIZWFkZXIiLCJQb3BvdmVyIiwiUG9wb3ZlclRyaWdnZXIiLCJQb3BvdmVyQ29udGVudCIsIlVwZGF0ZVRyYWNrU2hlZXQiLCJDb3B5IiwiQ2hlY2siLCJBbGVydE9jdGFnb24iLCJBbGVydFRyaWFuZ2xlIiwiQWxlcnRDaXJjbGUiLCJ1c2VTdGF0ZSIsInRvYXN0IiwiRGVsZXRlUm93IiwidHJhY2tTaGVldHNfcm91dGVzIiwiUGVybWlzc2lvbldyYXBwZXIiLCJDcmVhdGVNYW5pZmVzdERldGFpbCIsIk1kQ3JlYXRlTmV3Rm9sZGVyIiwiQnV0dG9uIiwiUmVhY3QiLCJyZWdpc3Rlck1vZHVsZXMiLCJDb3B5QnV0dG9uIiwidGV4dCIsImRpc2FibGVkIiwiY29waWVkIiwic2V0Q29waWVkIiwiaGFuZGxlQ29weSIsImUiLCJzdG9wUHJvcGFnYXRpb24iLCJlcnJvciIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsInN1Y2Nlc3MiLCJzZXRUaW1lb3V0IiwiZXJyIiwiY29uc29sZSIsIm9vX3R4IiwiYnV0dG9uIiwib25DbGljayIsImNsYXNzTmFtZSIsInRpdGxlIiwiV2FybmluZ0ljb25XaXRoUG9wb3ZlciIsIndhcm5pbmdzIiwibGVuZ3RoIiwiZ3JvdXBlZCIsInJlZHVjZSIsImFjYyIsInciLCJzZXZlcml0eSIsInB1c2giLCJhc0NoaWxkIiwic3BhbiIsImRpdiIsIm1hcCIsInNldiIsInVsIiwiaSIsImxpIiwibWVzc2FnZSIsImRpc3BsYXlOYW1lIiwiTWFuaWZlc3RBY3Rpb25CdXR0b24iLCJUcmFja1NoZWV0IiwidXNlckRhdGEiLCJpc0RpYWxvZ09wZW4iLCJzZXRJc0RpYWxvZ09wZW4iLCJtYW5pZmVzdERldGFpbFJlZiIsInVzZVJlZiIsImhhbmRsZU9wZW5EaWFsb2ciLCJjdXJyZW50IiwiZmV0Y2hNYW5pZmVzdERldGFpbHMiLCJpZCIsInZhcmlhbnQiLCJyZWYiLCJ0cmFja1NoZWV0SWQiLCJDb2x1bW4iLCJwZXJtaXNzaW9ucyIsInNldERlbGV0ZWREYXRhIiwiZGVsZXRlRGF0YSIsImNhcnJpZXJEYXRhVXBkYXRlIiwiY2xpZW50RGF0YVVwZGF0ZSIsImN1c3RvbUZpZWxkc01hcCIsInNob3dPcmNhQ29sdW1ucyIsInNob3dMZWdyYW5kQ29sdW1ucyIsImJhc2VDb2x1bW5zIiwiZmllbGQiLCJoZWFkZXJOYW1lIiwidmFsdWVHZXR0ZXIiLCJwYXJhbXMiLCJkYXRhIiwiY2xpZW50IiwiY2xpZW50X25hbWUiLCJmaWx0ZXIiLCJmaWx0ZXJQYXJhbXMiLCJidXR0b25zIiwid2lkdGgiLCJoZWFkZXJDb21wb25lbnQiLCJjb21wYW55IiwiZGl2aXNpb24iLCJjYXJyaWVyIiwibmFtZSIsImZ0cEZpbGVOYW1lIiwiZnRwUGFnZSIsImNlbGxSZW5kZXJlciIsImZpbGVQYXRoIiwiaGFzRmlsZVBhdGgiLCJkaXNwbGF5VGV4dCIsInN0eWxlIiwibWluV2lkdGgiLCJjZWxsU3R5bGUiLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsInBhZGRpbmciLCJoZWlnaHQiLCJib3JkZXJSaWdodCIsIndoaXRlU3BhY2UiLCJvdmVyZmxvdyIsInRleHRPdmVyZmxvdyIsInRvb2x0aXBWYWx1ZUdldHRlciIsIm1hc3Rlckludm9pY2UiLCJpbnZvaWNlIiwiYm9sIiwidmFsdWVGb3JtYXR0ZXIiLCJ2YWx1ZSIsImR0IiwiZnJvbUlTTyIsInpvbmUiLCJpc1ZhbGlkIiwidG9Gb3JtYXQiLCJjb21wYXJhdG9yIiwiZmlsdGVyTG9jYWxEYXRlQXRNaWRuaWdodCIsImNlbGxWYWx1ZSIsImNlbGxEYXRlIiwic3RhcnRPZiIsInRvSlNEYXRlIiwiZmlsdGVyRGF0ZSIsIkRhdGUiLCJVVEMiLCJnZXRGdWxsWWVhciIsImdldE1vbnRoIiwiZ2V0RGF0ZSIsImRhdGUiLCJpbnZvaWNlRGF0ZSIsInNoaXBtZW50RGF0ZSIsImludm9pY2VUb3RhbCIsImN1cnJlbmN5IiwicXR5U2hpcHBlZCIsInF1YW50aXR5QmlsbGVkVGV4dCIsImludm9pY2VTdGF0dXMiLCJtYW51YWxNYXRjaGluZyIsImludm9pY2VUeXBlIiwid2VpZ2h0VW5pdE5hbWUiLCJzYXZpbmdzIiwiZnJlaWdodENsYXNzIiwiYmlsbFRvQ2xpZW50IiwiZG9jQXZhaWxhYmxlIiwibm90ZXMiLCJlbnRlcmVkQnkiLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsImZpZWxkSWQiLCJmaWVsZE1ldGEiLCJmaWVsZE5hbWUiLCJmaWVsZFR5cGUiLCJ0eXBlIiwiY3VzdG9tRmllbGRzIiwibWFuaWZlc3REZXRhaWxzIiwibWFuaWZlc3RTdGF0dXMiLCJtYW5pZmVzdERhdGUiLCJhY3Rpb25SZXF1aXJlZCIsIm1hbmlmZXN0Tm90ZXMiLCJmcmVpZ2h0VGVybSIsInN5c3RlbUdlbmVyYXRlZFdhcm5pbmdzIiwic29ydGFibGUiLCJqdXN0aWZ5Q29udGVudCIsInRyYWNrU2hlZXQiLCJyZXF1aXJlZFBlcm1pc3Npb25zIiwicm91dGUiLCJERUxFVEVfVFJBQ0tfU0hFRVRTIiwib25TdWNjZXNzIiwicGlubmVkIiwiZm9udEZhbWlseSIsImNvbG9yIiwiZm9udFN0eWxlIiwib29fY20iLCJldmFsIiwib29fb28iLCJ2IiwiY29uc29sZUxvZyIsIm9vX3RyIiwiY29uc29sZVRyYWNlIiwiY29uc29sZUVycm9yIiwib29fdHMiLCJjb25zb2xlVGltZSIsIm9vX3RlIiwiY29uc29sZVRpbWVFbmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/column.tsx\n"));

/***/ })

});