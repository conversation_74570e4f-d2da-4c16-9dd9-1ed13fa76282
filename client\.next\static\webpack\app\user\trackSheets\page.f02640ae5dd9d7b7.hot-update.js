"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/_component/DataGridTableTrackSheet.tsx":
/*!****************************************************!*\
  !*** ./app/_component/DataGridTableTrackSheet.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=FaFilter,FaSearch!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _Pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Pagination */ \"(app-pages-browser)/./app/_component/Pagination.tsx\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-react */ \"(app-pages-browser)/./node_modules/ag-grid-react/dist/package/index.esm.mjs\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles/ag-grid.css */ \"(app-pages-browser)/./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles/ag-theme-alpine.css */ \"(app-pages-browser)/./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_customInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/customInput */ \"(app-pages-browser)/./components/ui/customInput.tsx\");\n/* harmony import */ var _barrel_optimize_names_BiHide_BiShow_react_icons_bi__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BiHide,BiShow!=!react-icons/bi */ \"(app-pages-browser)/./node_modules/react-icons/bi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LuSearchX_react_icons_lu__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=LuSearchX!=!react-icons/lu */ \"(app-pages-browser)/./node_modules/react-icons/lu/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _user_trackSheets_ticketing_system_CreateTicket__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../user/trackSheets/ticketing_system/CreateTicket */ \"(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle2_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle2,ExternalLink!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle2_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle2,ExternalLink!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle2_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle2,ExternalLink!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _user_trackSheets_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../user/trackSheets/TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_MdDensitySmall_react_icons_md__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=MdDensitySmall!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom cell renderer for selection checkbox\nconst SelectionCheckboxRenderer = (props)=>{\n    var _props_data;\n    const isSelected = props.node.isSelected();\n    const ticketId = (_props_data = props.data) === null || _props_data === void 0 ? void 0 : _props_data.ticketId;\n    const handleChange = (checked)=>{\n        props.node.setSelected(checked, false, true);\n    };\n    if (ticketId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center w-full h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/pms/manage_tickets/\".concat(ticketId, \"?from=tracksheets\"),\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                tabIndex: 0,\n                                className: \"focus:outline-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"flex items-center justify-center w-[22px] h-[22px] p-0.5 border-green-200 bg-green-50 text-green-700 hover:bg-green-100 transition cursor-pointer\",\n                                    style: {\n                                        minWidth: 22,\n                                        minHeight: 22\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle2_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                            side: \"right\",\n                            className: \"text-xs max-w-[180px]\",\n                            children: [\n                                \"A ticket already exists for this row.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Click to view the ticket in a new tab.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n            checked: isSelected,\n            onCheckedChange: handleChange,\n            \"aria-label\": \"Select row\",\n            className: \"h-4 w-4 border-2 border-primary rounded-sm bg-white\"\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SelectionCheckboxRenderer;\n// Custom header component for \"select all\" functionality\nconst HeaderSelectionCheckboxRenderer = (props)=>{\n    _s();\n    const [isChecked, setIsChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIndeterminate, setIsIndeterminate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const onSelectionChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const selectedNodes = props.api.getSelectedNodes();\n        let displayedNodeCount = 0;\n        props.api.forEachNodeAfterFilterAndSort(()=>{\n            displayedNodeCount++;\n        });\n        if (displayedNodeCount === 0) {\n            setIsChecked(false);\n            setIsIndeterminate(false);\n            return;\n        }\n        const allSelected = selectedNodes.length === displayedNodeCount;\n        const someSelected = selectedNodes.length > 0 && !allSelected;\n        setIsChecked(allSelected);\n        setIsIndeterminate(someSelected);\n    }, [\n        props.api\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        props.api.addEventListener(\"selectionChanged\", onSelectionChanged);\n        props.api.addEventListener(\"modelUpdated\", onSelectionChanged); // for filtering, sorting etc\n        return ()=>{\n            props.api.removeEventListener(\"selectionChanged\", onSelectionChanged);\n            props.api.removeEventListener(\"modelUpdated\", onSelectionChanged);\n        };\n    }, [\n        onSelectionChanged,\n        props.api\n    ]);\n    const handleChange = (checked)=>{\n        if (checked) {\n            props.api.forEachNodeAfterFilterAndSort((node)=>{\n                var _node_data;\n                if (!((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.ticketId)) {\n                    node.setSelected(true);\n                } else {\n                    node.setSelected(false);\n                }\n            });\n        } else {\n            props.api.forEachNodeAfterFilterAndSort((node)=>node.setSelected(false));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n            checked: isIndeterminate ? \"indeterminate\" : isChecked,\n            onCheckedChange: handleChange,\n            \"aria-label\": \"Select all rows\",\n            className: \"h-4 w-4 border-2 border-primary rounded-sm bg-white\"\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeaderSelectionCheckboxRenderer, \"jNhmhHqHBjDe+xUOk7p4amZYv0w=\");\n_c1 = HeaderSelectionCheckboxRenderer;\nconst DataGridTableTrackSheet = (param)=>{\n    let { columns, data, isLoading, showColDropDowns, filter, filter2, filter3 = false, filter3view, filter4, filter4view, filter5, filter5view, total, totalview, filter_column, filter_column2, filter_column3, filter_column4, totalPages, showPageEntries, className, filter1PlaceHolder, showSearchColumn = true, pageSize, customFieldsMap, selectedClients, showLegrandColumns, onColumnVisibilityChange } = param;\n    var _gridRef_current_api, _gridRef_current, _gridRef_current_api1, _gridRef_current1;\n    _s1();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(pageSize || 50);\n    const [selectedColumns, setSelectedColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isTicketModalOpen, setIsTicketModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const params = new URLSearchParams(searchParams);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { replace } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPage = Number(searchParams.get(\"page\")) || 1;\n    const currentPageSize = Number(searchParams.get(\"pageSize\")) || pageSize || 50;\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [columnData, setColumnData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columnVisibility, setColumnVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputValues, setInputValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [searchTerms, setSearchTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [filtersCollapsed, setFiltersCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const COLLAPSE_COUNT = 6;\n    const onSelectionChanged = ()=>{\n        if (gridRef.current && gridRef.current.api) {\n            setSelectedRows(gridRef.current.api.getSelectedRows());\n        }\n    };\n    const handleClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.deselectAll();\n        }\n        setSelectedRows([]);\n        setIsTicketModalOpen(false);\n    }, []);\n    const processedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return data === null || data === void 0 ? void 0 : data.map((item, index)=>({\n                ...item,\n                stableId: (currentPage - 1) * currentPageSize + index + 1\n            }));\n    }, [\n        data,\n        currentPage,\n        currentPageSize\n    ]);\n    const columnsWithSerialNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return [\n            {\n                width: 80,\n                pinned: \"left\",\n                lockVisible: true,\n                suppressHeaderMenuButton: true,\n                headerName: \"\",\n                headerComponent: HeaderSelectionCheckboxRenderer,\n                cellRenderer: SelectionCheckboxRenderer,\n                suppressMovable: true,\n                suppressSizeToFit: true,\n                resizable: false,\n                suppressNavigable: true,\n                suppressMenu: true,\n                suppressColumnsToolPanel: true,\n                suppressAutoSize: true,\n                suppressCellSelection: true\n            },\n            {\n                headerName: \"Sr. No.\",\n                field: \"stableId\",\n                sortable: true,\n                width: 80,\n                minWidth: 80,\n                maxWidth: 80,\n                cellStyle: {\n                    textAlign: \"center\"\n                },\n                pinned: \"left\",\n                comparator: (valueA, valueB)=>valueA - valueB\n            },\n            ...columns.filter((col)=>col.field !== \"sr_no\")\n        ];\n    }, [\n        columns\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize visibility state for all columns only once\n        if (!isInitialized) {\n            const initialVisibility = {};\n            columnsWithSerialNumber.forEach((col)=>{\n                initialVisibility[col.field] = true;\n            });\n            setColumnVisibility(initialVisibility);\n            onColumnVisibilityChange === null || onColumnVisibilityChange === void 0 ? void 0 : onColumnVisibilityChange(initialVisibility);\n            setIsInitialized(true);\n        }\n    }, [\n        columnsWithSerialNumber,\n        isInitialized,\n        onColumnVisibilityChange\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            if (isLoading) {\n                gridRef.current.api.showLoadingOverlay();\n            } else if (!processedData || processedData.length === 0) {\n                gridRef.current.api.showNoRowsOverlay();\n            } else {\n                gridRef.current.api.hideOverlay();\n            }\n        }\n    }, [\n        isLoading,\n        processedData\n    ]);\n    const handlePageChange = (e)=>{\n        const newPageSize = parseInt(e.target.value);\n        setPage(newPageSize);\n        if (totalPages) {\n            params.set(\"pageSize\", newPageSize === null || newPageSize === void 0 ? void 0 : newPageSize.toString());\n            replace(\"\".concat(pathname, \"?\").concat(params.toString()));\n        }\n    };\n    const handleColumnSelection = (columnKey, columnHeader)=>{\n        setSelectedColumns((prevSelectedColumns)=>{\n            let updatedColumns;\n            if (prevSelectedColumns.includes(columnKey)) {\n                updatedColumns = prevSelectedColumns.filter((col)=>col !== columnKey);\n                const updatedParams = new URLSearchParams(searchParams);\n                if (columnHeader === \"Received Date\") {\n                    updatedParams.delete(\"recievedFDate\");\n                    updatedParams.delete(\"recievedTDate\");\n                } else {\n                    updatedParams.delete(columnHeader);\n                }\n                replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            } else {\n                updatedColumns = [\n                    ...prevSelectedColumns,\n                    columnKey\n                ];\n                const updatedParams = new URLSearchParams(searchParams);\n                if (columnHeader === \"Received Date\") {\n                    updatedParams.set(\"recievedFDate\", \"\");\n                    updatedParams.set(\"recievedTDate\", \"\");\n                } else {\n                    updatedParams.set(columnHeader, \"\");\n                }\n                replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            }\n            return updatedColumns;\n        });\n    };\n    const handleKeyDown = (e, columnName)=>{\n        var _inputValues_columnName, _searchTerms_columnName;\n        let paramKey = columnName;\n        if (!paramKey.startsWith(\"customField_\")) {\n            const col = columnsWithSerialNumber.find((c)=>c.headerName === columnName || c.field === columnName);\n            if (col && col.field && col.field.startsWith(\"customField_\")) {\n                paramKey = col.field;\n            }\n        }\n        const currentInput = (_inputValues_columnName = inputValues[columnName]) === null || _inputValues_columnName === void 0 ? void 0 : _inputValues_columnName.trim();\n        if (e.key === \"Enter\") {\n            var _searchTerms_columnName1;\n            e.preventDefault();\n            if (currentInput && !((_searchTerms_columnName1 = searchTerms[columnName]) === null || _searchTerms_columnName1 === void 0 ? void 0 : _searchTerms_columnName1.includes(currentInput))) {\n                const updated = [\n                    ...searchTerms[columnName] || [],\n                    currentInput\n                ];\n                updateSearchParams(columnName, updated);\n                setSearchTerms({\n                    ...searchTerms,\n                    [columnName]: updated\n                });\n                setInputValues({\n                    ...inputValues,\n                    [columnName]: \"\"\n                });\n            }\n        }\n        if (e.key === \"Backspace\" && !currentInput && ((_searchTerms_columnName = searchTerms[columnName]) === null || _searchTerms_columnName === void 0 ? void 0 : _searchTerms_columnName.length) > 0) {\n            const updated = [\n                ...searchTerms[columnName]\n            ];\n            updated.pop();\n            updateSearchParams(columnName, updated);\n            setSearchTerms({\n                ...searchTerms,\n                [columnName]: updated\n            });\n        }\n    };\n    const handleRemoveTerm = (columnName, term)=>{\n        const col = columnsWithSerialNumber.find((c)=>c.headerName === columnName || c.field === columnName);\n        const key = col ? col.headerName : columnName;\n        let paramKey = columnName;\n        if (!paramKey.startsWith(\"customField_\")) {\n            if (col && col.field && col.field.startsWith(\"customField_\")) {\n                paramKey = col.field;\n            }\n        }\n        if (paramKey.startsWith(\"customField_\")) {\n            const newSearchTerms = {\n                ...searchTerms\n            };\n            Object.keys(newSearchTerms).forEach((k)=>{\n                if (k === key || k === paramKey || k.startsWith(paramKey)) {\n                    newSearchTerms[k] = [];\n                }\n            });\n            setSearchTerms(newSearchTerms);\n            setInputValues((prev)=>{\n                const newValues = {\n                    ...prev\n                };\n                Object.keys(newValues).forEach((k)=>{\n                    if (k === key || k === paramKey || k.startsWith(paramKey)) {\n                        newValues[k] = \"\";\n                    }\n                });\n                return newValues;\n            });\n            const updatedParams = new URLSearchParams(searchParams);\n            updatedParams.delete(paramKey);\n            replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            return;\n        }\n        const updated = (searchTerms[paramKey] || []).filter((t)=>t !== term);\n        updateSearchParams(paramKey, updated);\n        setSearchTerms({\n            ...searchTerms,\n            [paramKey]: updated\n        });\n    };\n    const updateSearchParams = (columnName, terms)=>{\n        var _gridRef_current;\n        let paramKey = columnName;\n        if (!paramKey.startsWith(\"customField_\")) {\n            const col = columnsWithSerialNumber.find((c)=>c.headerName === columnName || c.field === columnName);\n            if (col && col.field && col.field.startsWith(\"customField_\")) {\n                paramKey = col.field;\n            }\n        }\n        const updatedParams = new URLSearchParams(searchParams);\n        if (terms.length > 0) {\n            updatedParams.set(paramKey, terms.join(\",\"));\n            updatedParams.set(\"page\", \"1\");\n        } else {\n            updatedParams.delete(paramKey);\n        }\n        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n        if ((_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : _gridRef_current.api) {\n            gridRef.current.api.setColumnsVisible([\n                columnName\n            ], terms.length > 0);\n        }\n    };\n    const filterColumns = selectedColumns.length ? selectedColumns.map((columnKey)=>columnsWithSerialNumber.find((col)=>col.field === columnKey)).filter((col)=>col !== undefined) : [];\n    const toggleColumnVisibility = (field, isVisible)=>{\n        var _gridRef_current;\n        setColumnVisibility((prevVisibility)=>{\n            const newVisibility = {\n                ...prevVisibility,\n                [field]: isVisible\n            };\n            onColumnVisibilityChange === null || onColumnVisibilityChange === void 0 ? void 0 : onColumnVisibilityChange(newVisibility);\n            return newVisibility;\n        });\n        // Update AG Grid directly without using effect\n        if ((_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : _gridRef_current.api) {\n            gridRef.current.api.setColumnsVisible([\n                field\n            ], isVisible);\n        }\n    };\n    const { warningFilter, setWarningFilter } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_user_trackSheets_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__.TrackSheetContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"animate-in fade-in duration-1000\",\n        children: [\n            selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 left-1/2 -translate-x-1/2 z-20 bg-background border border-primary shadow-lg rounded-lg px-4 py-2 flex items-center gap-4 animate-in fade-in slide-in-from-bottom-2 duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-primary\",\n                        children: [\n                            selectedRows.length,\n                            \" item(s) selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        variant: \"default\",\n                        size: \"sm\",\n                        onClick: ()=>setIsTicketModalOpen(true),\n                        children: \"Create Ticket\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"h-7 w-7\",\n                        onClick: handleClearSelection,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuSearchX_react_icons_lu__WEBPACK_IMPORTED_MODULE_18__.LuSearchX, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Clear Selection\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 506,\n                columnNumber: 9\n            }, undefined),\n            isTicketModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_trackSheets_ticketing_system_CreateTicket__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: isTicketModalOpen,\n                onClose: ()=>setIsTicketModalOpen(false),\n                onClearSelection: handleClearSelection,\n                selectedRows: selectedRows\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 530,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between w-full lg:w-full lg:flex lg:justify-end mb-3 animate-in fade-in duration-1000 gap-5\",\n                children: [\n                    selectedClients.length > 0 && showLegrandColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-[320px] h-10  rounded-full flex items-center p-1 shadow-inner border  gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1 left-1 h-6 w-[105px] rounded-full transition-all duration-300 ease-in-out shadow\\n          \".concat(warningFilter === \"true\" ? \"translate-x-0 bg-red-100 border border-red-300\" : warningFilter === \"false\" ? \"translate-x-[102px] bg-green-100 border border-green-300\" : \"translate-x-[205px] border border-gray-300\", \"\\n        \")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setWarningFilter(\"true\"),\n                                    \"aria-pressed\": warningFilter === \"true\",\n                                    className: \"relative z-10 flex-1 h-8 text-xs font-medium rounded-full flex items-center justify-center gap-1 focus:outline-none \\n          \".concat(warningFilter === \"true\" ? \"text-red-700 font-semibold\" : \"text-gray-600 hover:text-red-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle2_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 \".concat(warningFilter === \"true\" ? \"text-red-600\" : \"text-red-400\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Warnings\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setWarningFilter(\"false\"),\n                                    \"aria-pressed\": warningFilter === \"false\",\n                                    className: \"relative z-10 flex-1 h-8 text-xs font-medium rounded-full flex items-center justify-center gap-1 focus:outline-none\\n          \".concat(warningFilter === \"false\" ? \"text-green-700 font-semibold\" : \"text-gray-600 hover:text-green-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle2_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-4 h-4 \".concat(warningFilter === \"false\" ? \"text-green-600\" : \"text-green-400\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"No Warnings\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setWarningFilter(null),\n                                    \"aria-pressed\": warningFilter === null,\n                                    className: \"relative z-10 flex-1 h-8 text-xs font-medium rounded-full flex items-center justify-center gap-1 focus:outline-none\\n          \".concat(warningFilter === null ? \"text-gray-800 font-semibold\" : \"text-gray-600 hover:text-gray-800\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdDensitySmall_react_icons_md__WEBPACK_IMPORTED_MODULE_21__.MdDensitySmall, {\n                                            className: \"w-3 h-3 \".concat(warningFilter === null ? \"text-gray-700\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Show All\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, undefined),\n                    filtersCollapsed && showPageEntries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: page,\n                        onChange: handlePageChange,\n                        className: \" border-2 rounded-md  items-center justify-center cursor-pointer h-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 10,\n                                children: \"10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 15,\n                                children: \"15\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 25,\n                                children: \"25\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 50,\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 100,\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 250,\n                                children: \"250\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 500,\n                                children: \"500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 1000,\n                                children: \"1000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 1500,\n                                children: \"1500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 2000,\n                                children: \"2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, undefined),\n                    filtersCollapsed && showColDropDowns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"border-2 rounded-md flex items-center justify-center  cursor-pointer h-10 w-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_22__.FaFilter, {}, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 overflow-y-auto max-h-80\",\n                                onSelect: (e)=>e.preventDefault(),\n                                children: columnsWithSerialNumber.filter((column)=>column.hideable !== false).map((column)=>/*#__PURE__*/ {\n                                    var _columnVisibility_column_field;\n                                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuCheckboxItem, {\n                                        className: \"capitalize cursor-pointer\",\n                                        checked: (_columnVisibility_column_field = columnVisibility[column.field]) !== null && _columnVisibility_column_field !== void 0 ? _columnVisibility_column_field : true,\n                                        onCheckedChange: (value)=>{\n                                            toggleColumnVisibility(column.field, value);\n                                        },\n                                        onSelect: (e)=>e.preventDefault(),\n                                        children: column.headerName || column.field\n                                    }, column.field, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 11\n                    }, undefined),\n                    filtersCollapsed && showSearchColumn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"border-2 rounded-md flex items-center justify-center  cursor-pointer h-10 w-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_22__.FaSearch, {\n                                        className: \"text-base\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"mt-2 bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 max-h-96 min-w-[260px]\",\n                                onSelect: (e)=>e.preventDefault(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 sticky top-0 z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-semibold text-sm\",\n                                                        children: \"Select Columns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            selectedColumns.length,\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Search columns...\",\n                                                value: columnData,\n                                                onChange: (e)=>setColumnData(e.target.value),\n                                                className: \"mt-1 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 mb-2 \",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-xs text-blue-600 underline\",\n                                                    type: \"button\",\n                                                    onClick: ()=>setSelectedColumns(columnsWithSerialNumber.filter((col)=>col.field !== \"action\" && col.field !== \"sr_no\" && col.field !== \"stableId\").map((col)=>col.field)),\n                                                    children: \"Select All\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-y-auto max-h-60\",\n                                        children: columnsWithSerialNumber.filter((item)=>{\n                                            var _this;\n                                            return item.field !== \"action\" && item.field !== \"sr_no\" && item.field !== \"stableId\" && (!columnData || ((_this = item.headerName || item.field) === null || _this === void 0 ? void 0 : _this.toLowerCase().includes(columnData.toLowerCase())));\n                                        }).map((item, id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuCheckboxItem, {\n                                                checked: selectedColumns.includes(item.field),\n                                                onCheckedChange: ()=>handleColumnSelection(item.field, item.headerName || item.field),\n                                                className: \"capitalize cursor-pointer\",\n                                                onSelect: (e)=>e.preventDefault(),\n                                                children: item.headerName\n                                            }, id, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 11\n                    }, undefined),\n                    filterColumns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-nowrap gap-2 items-center overflow-x-auto bg-gray-50 border border-gray-200 rounded-md py-2 px-2\",\n                            style: {\n                                scrollbarWidth: \"thin\",\n                                minHeight: 50\n                            },\n                            children: [\n                                filterColumns.length > COLLAPSE_COUNT && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 sticky left-0 z-10 bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs border border-gray-300 mr-2\",\n                                        type: \"button\",\n                                        onClick: ()=>setFiltersCollapsed((prev)=>!prev),\n                                        children: filtersCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BiHide_BiShow_react_icons_bi__WEBPACK_IMPORTED_MODULE_23__.BiShow, {\n                                            className: \"w-5 h-5\",\n                                            title: \"Show Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 23\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BiHide_BiShow_react_icons_bi__WEBPACK_IMPORTED_MODULE_23__.BiHide, {\n                                            className: \"w-5 h-5\",\n                                            title: \"Hide Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 17\n                                }, undefined),\n                                filterColumns.filter((col)=>col && col.field) // Defensive: filter out undefined/null or missing field\n                                .slice(0, filtersCollapsed && filterColumns.length > COLLAPSE_COUNT ? COLLAPSE_COUNT : filterColumns.length).map((col, index)=>{\n                                    var _customFieldsMap_customFieldId_type, _customFieldsMap_customFieldId, _searchTerms_col_headerName, _searchTerms_col_headerName1;\n                                    if (col.headerName === \"Received Date\") {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Recieved Date (From)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"recievedFDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"recievedFDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Recieved Date (To)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"recievedTDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"recievedTDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(\"recievedFDate\") || searchParams.get(\"recievedTDate\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2\",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(\"recievedFDate\");\n                                                        updatedParams.delete(\"recievedTDate\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    if (col.headerName === \"Invoice Date\") {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Invoice Date (From)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"invoiceFDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"invoiceFDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Invoice Date (To)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"invoiceTDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"invoiceTDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(\"invoiceFDate\") || searchParams.get(\"invoiceTDate\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2\",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(\"invoiceFDate\");\n                                                        updatedParams.delete(\"invoiceTDate\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    if (col.headerName === \"Shipment Date\") {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Shipment Date (From)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"shipmentFDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"shipmentFDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"Shipment Date (To)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(\"shipmentTDate\") || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.set(\"shipmentTDate\", e.target.value);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(\"shipmentFDate\") || searchParams.get(\"shipmentTDate\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2 \",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(\"shipmentFDate\");\n                                                        updatedParams.delete(\"shipmentTDate\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 929,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    const isCustomField = col.field.startsWith(\"customField_\");\n                                    const customFieldId = isCustomField ? col.field.replace(\"customField_\", \"\") : null;\n                                    const isDateType = isCustomField && customFieldsMap && ((_customFieldsMap_customFieldId = customFieldsMap[customFieldId]) === null || _customFieldsMap_customFieldId === void 0 ? void 0 : (_customFieldsMap_customFieldId_type = _customFieldsMap_customFieldId.type) === null || _customFieldsMap_customFieldId_type === void 0 ? void 0 : _customFieldsMap_customFieldId_type.toLowerCase()) === \"date\";\n                                    if (isDateType) {\n                                        const fromKey = \"customField_\".concat(customFieldId, \"_from\");\n                                        const toKey = \"customField_\".concat(customFieldId, \"_to\");\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col min-w-[180px] relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: [\n                                                        customFieldsMap[customFieldId].name,\n                                                        \" (From)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 967,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(fromKey) || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        if (e.target.value) updatedParams.set(fromKey, e.target.value);\n                                                        else updatedParams.delete(fromKey);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"mb-1 pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: [\n                                                        customFieldsMap[customFieldId].name,\n                                                        \" (To)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 985,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"date\",\n                                                    value: searchParams.get(toKey) || \"\",\n                                                    onChange: (e)=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        if (e.target.value) updatedParams.set(toKey, e.target.value);\n                                                        else updatedParams.delete(toKey);\n                                                        updatedParams.set(\"page\", \"1\");\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    className: \"pr-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 988,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (searchParams.get(fromKey) || searchParams.get(toKey)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"mt-2 absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg\",\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const updatedParams = new URLSearchParams(searchParams);\n                                                        updatedParams.delete(fromKey);\n                                                        updatedParams.delete(toKey);\n                                                        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                                    },\n                                                    title: \"Clear\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex- w-full relative  \".concat(filtersCollapsed ? \"min-w-[100px] md:w-[calc(20%-1rem)]\" : \"min-w-[220px] md:w-[calc(25%-1rem)]\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap items-center gap-1 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md  px-1 focus-within:ring-0 focus-within:ring-blue-600 focus-within:border-blue-600 shadow-sm\",\n                                                children: [\n                                                    ((_searchTerms_col_headerName = searchTerms[col === null || col === void 0 ? void 0 : col.headerName]) === null || _searchTerms_col_headerName === void 0 ? void 0 : _searchTerms_col_headerName.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: searchTerms[col.headerName].map((term, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \" flex items-center bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 rounded-full text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-1 truncate max-w-xs\",\n                                                                        children: term\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                                        lineNumber: 1044,\n                                                                        columnNumber: 35\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: (e)=>{\n                                                                            e.preventDefault();\n                                                                            handleRemoveTerm(col.headerName, term);\n                                                                        },\n                                                                        className: \"  text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100\",\n                                                                        title: \"Remove\",\n                                                                        children: \"\\xd7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                                        lineNumber: 1047,\n                                                                        columnNumber: 35\n                                                                    }, undefined)\n                                                                ]\n                                                            }, i, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                                lineNumber: 1040,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_customInput__WEBPACK_IMPORTED_MODULE_10__.CustomInput, {\n                                                        value: inputValues[col === null || col === void 0 ? void 0 : col.headerName] || \"\",\n                                                        onChange: (e)=>setInputValues({\n                                                                ...inputValues,\n                                                                [col.headerName]: e.target.value\n                                                            }),\n                                                        onKeyDown: (e)=>handleKeyDown(e, col.headerName),\n                                                        placeholder: ((_searchTerms_col_headerName1 = searchTerms[col === null || col === void 0 ? void 0 : col.headerName]) === null || _searchTerms_col_headerName1 === void 0 ? void 0 : _searchTerms_col_headerName1.length) > 0 ? \"\" : \"Search \".concat(col.headerName, \"...\"),\n                                                        className: \"flex-1 min-w-[30px] bg-transparent border-none focus:outline-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 1061,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    inputValues[col.headerName] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"ml-1 text-gray-400 hover:text-gray-700 text-lg\",\n                                                        type: \"button\",\n                                                        onClick: ()=>{\n                                                            setInputValues({\n                                                                ...inputValues,\n                                                                [col.headerName]: \"\"\n                                                            });\n                                                            handleRemoveTerm(col.headerName, inputValues[col.headerName]);\n                                                        },\n                                                        title: \"Clear\",\n                                                        children: \"\\xd7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 1035,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 21\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 ml-2 sticky right-0 z-10 bg-gray-50 flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs border border-gray-300\",\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setInputValues({});\n                                            setSearchTerms({});\n                                            setSelectedColumns([]);\n                                            setColumnData(\"\");\n                                            setFiltersCollapsed(true);\n                                            setColumnVisibility({});\n                                            const updatedParams = new URLSearchParams(searchParams);\n                                            filterColumns.forEach((col)=>{\n                                                if (!col || !col.field) return; // Defensive: skip if col or col.field is missing\n                                                updatedParams.delete(col.headerName);\n                                                if (col.field.startsWith(\"customField_\")) {\n                                                    updatedParams.delete(col.field);\n                                                    updatedParams.delete(\"\".concat(col.field, \"_from\"));\n                                                    updatedParams.delete(\"\".concat(col.field, \"_to\"));\n                                                }\n                                                if (col.headerName === \"Received Date\") {\n                                                    updatedParams.delete(\"recievedFDate\");\n                                                    updatedParams.delete(\"recievedTDate\");\n                                                }\n                                                if (col.headerName === \"Invoice Date\") {\n                                                    updatedParams.delete(\"invoiceFDate\");\n                                                    updatedParams.delete(\"invoiceTDate\");\n                                                }\n                                                if (col.headerName === \"Shipment Date\") {\n                                                    updatedParams.delete(\"shipmentFDate\");\n                                                    updatedParams.delete(\"shipmentTDate\");\n                                                }\n                                            });\n                                            replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n                                        },\n                                        title: \"Reset All Filters\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuSearchX_react_icons_lu__WEBPACK_IMPORTED_MODULE_18__.LuSearchX, {\n                                            className: \"text-red-500 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                            lineNumber: 1138,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                        lineNumber: 1102,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                    lineNumber: 1101,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 11\n                    }, undefined),\n                    filter && filterColumns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: filterColumns.map((column, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: filter1PlaceHolder ? filter1PlaceHolder : filter_column,\n                                value: inputValues[column.headerName] || \"\",\n                                onChange: (e)=>setInputValues({\n                                        ...inputValues,\n                                        [column.headerName]: e.target.value\n                                    }),\n                                onKeyDown: (e)=>handleKeyDown(e, column.headerName),\n                                className: \"w-[20%] dark:bg-gray-700 !outline-main-color\"\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 1147,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false),\n                    filter2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: filterColumns.map((column, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"\".concat(filter_column2),\n                                value: inputValues[column.headerName] || \"\",\n                                onChange: (e)=>setInputValues({\n                                        ...inputValues,\n                                        [column.headerName]: e.target.value\n                                    }),\n                                onKeyDown: (e)=>handleKeyDown(e, column.headerName),\n                                className: \"w-[20%] dark:bg-gray-700 !outline-main-color\"\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                                lineNumber: 1168,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, undefined),\n            total && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: totalview\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 1186,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ag-theme-alpine custom-ag-grid\",\n                    style: {\n                        height: \"100vh\",\n                        width: \"100%\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_6__.AgGridReact, {\n                        ref: gridRef,\n                        rowData: processedData,\n                        columnDefs: columnsWithSerialNumber,\n                        headerHeight: 35,\n                        rowSelection: \"multiple\",\n                        suppressRowClickSelection: true,\n                        onSelectionChanged: onSelectionChanged,\n                        enableCellTextSelection: true,\n                        defaultColDef: {\n                            sortable: true,\n                            resizable: true,\n                            cellStyle: {\n                                borderRight: \"1px solid #ddd\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                        lineNumber: 1193,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                    lineNumber: 1189,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 1188,\n                columnNumber: 7\n            }, undefined),\n            data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Pagination__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                currentPage: totalPages ? Number(params.get(\"page\")) || 1 : (((_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : (_gridRef_current_api = _gridRef_current.api) === null || _gridRef_current_api === void 0 ? void 0 : _gridRef_current_api.paginationGetCurrentPage()) || 0) + 1,\n                totalPages: totalPages ? totalPages : ((_gridRef_current1 = gridRef.current) === null || _gridRef_current1 === void 0 ? void 0 : (_gridRef_current_api1 = _gridRef_current1.api) === null || _gridRef_current_api1 === void 0 ? void 0 : _gridRef_current_api1.paginationGetTotalPages()) || 1,\n                onPageChange: (page)=>{\n                    if (gridRef.current) {\n                        var _gridRef_current_api, _gridRef_current;\n                        gridRef === null || gridRef === void 0 ? void 0 : (_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : (_gridRef_current_api = _gridRef_current.api) === null || _gridRef_current_api === void 0 ? void 0 : _gridRef_current_api.paginationGoToPage(page - 1);\n                    }\n                    if (totalPages) {\n                        params.set(\"page\", page.toString());\n                        replace(\"\".concat(pathname, \"?\").concat(params.toString()));\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n                lineNumber: 1212,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTableTrackSheet.tsx\",\n        lineNumber: 504,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DataGridTableTrackSheet, \"CP4z04e5pLoiYKiNqiAO2xHXEow=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c2 = DataGridTableTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DataGridTableTrackSheet);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"SelectionCheckboxRenderer\");\n$RefreshReg$(_c1, \"HeaderSelectionCheckboxRenderer\");\n$RefreshReg$(_c2, \"DataGridTableTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/_component/DataGridTableTrackSheet.tsx\n"));

/***/ })

});